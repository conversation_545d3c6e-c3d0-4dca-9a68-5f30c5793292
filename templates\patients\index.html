{% extends "base.html" %}

{% block page_title %}إدارة المرضى{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة المرضى</h5>
        <div>
            <!-- Add Export/Import Buttons -->
            <div class="btn-group me-2">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-file-export me-1"></i> تصدير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('patients.export_excel') }}">Excel</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('patients.export_pdf') }}">PDF</a></li>
                </ul>
            </div>
            
            <a href="{{ url_for('patients.import_excel') }}" class="btn btn-outline-success me-2">
                <i class="fas fa-file-import me-1"></i> استيراد
            </a>
            
            <a href="{{ url_for('patients.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> إضافة مريض جديد
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- Search Box -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" id="patientSearch" class="form-control" placeholder="ابحث عن مريض...">
                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Existing table code -->
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>تاريخ الميلاد</th>
                        <th>الجنس</th>
                        <th>رقم الهاتف</th>
                        <th>فصيلة الدم</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if patients %}
                        {% for patient in patients %}
                        <tr>
                            <td>{{ patient.id }}</td>
                            <td>{{ patient.name }}</td>
                            <td>{{ patient.birth_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ patient.gender }}</td>
                            <td>{{ patient.phone }}</td>
                            <td>{{ patient.blood_type }}</td>
                            <td>{{ patient.created_at.strftime('%Y-%m-%d') }}</td>
                            <!-- Replace the existing delete button with this -->
                            <td>
                                <a href="{{ url_for('patients.view', patient_id=patient.id) }}" class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></a>
                                <a href="{{ url_for('patients.edit', patient_id=patient.id) }}" class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></a>
                                <!-- Use a button to trigger the modal instead of direct form submission -->
                                <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal{{ patient.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                                
                                <!-- Delete Confirmation Modal -->
                                <div class="modal fade" id="deleteModal{{ patient.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ patient.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ patient.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من حذف المريض "{{ patient.name }}"؟ سيتم حذف جميع البيانات المرتبطة به.
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('patients.delete', patient_id=patient.id) }}" method="POST" style="display: inline;">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="8" class="text-center">لا يوجد مرضى مسجلين</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add JavaScript for search functionality -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('patientSearch');
        const clearButton = document.getElementById('clearSearch');
        const tableRows = document.querySelectorAll('table tbody tr');
        
        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            
            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if(text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
        
        clearButton.addEventListener('click', function() {
            searchInput.value = '';
            tableRows.forEach(row => {
                row.style.display = '';
            });
        });
    });
</script>
{% endblock %}