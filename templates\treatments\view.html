{% extends "base.html" %}

{% block page_title %}تفاصيل العلاج{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تفاصيل العلاج</h5>
        <div>
            <a href="{{ url_for('treatments.edit', treatment_id=treatment.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <a href="{{ url_for('treatments.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">المريض:</h6>
                <p>{{ treatment.patient.name }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">الطبيب:</h6>
                <p>{{ treatment.doctor.name }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">اسم الدواء:</h6>
                <p>{{ treatment.medication_name }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">الجرعة:</h6>
                <p>{{ treatment.dosage }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">عدد مرات الاستخدام:</h6>
                <p>{{ treatment.frequency or '-' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ البدء:</h6>
                <p>{{ treatment.start_date.strftime('%Y-%m-%d') if treatment.start_date else '-' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ الانتهاء:</h6>
                <p>{{ treatment.end_date.strftime('%Y-%m-%d') if treatment.end_date else '-' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ الإضافة:</h6>
                <p>{{ treatment.created_at.strftime('%Y-%m-%d') if treatment.created_at else '-' }}</p>
            </div>
            <div class="col-md-12 mb-3">
                <h6 class="fw-bold">تعليمات الاستخدام:</h6>
                <p>{{ treatment.instructions or '-' }}</p>
            </div>
            <div class="col-md-12 mb-3">
                <h6 class="fw-bold">ملاحظات إضافية:</h6>
                <p>{{ treatment.notes or '-' }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}