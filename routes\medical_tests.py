from flask import Blueprint, render_template, redirect, url_for, request, flash
from flask_login import login_required, current_user
from models import db, MedicalTest, Patient
from datetime import datetime
from sqlalchemy import func
from flask import jsonify, request
import json
from sqlalchemy import or_

medical_tests_bp = Blueprint('medical_tests', __name__)

@medical_tests_bp.route('/')
@login_required
def index():
    # Get filter parameters
    patient_id = request.args.get('patient')
    test_date = request.args.get('test_date')
    search_query = request.args.get('search', '')
    
    # Build query
    query = MedicalTest.query.join(Patient)
    
    # Apply search if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            or_(
                Patient.name.ilike(search_term),
                Patient.phone.ilike(search_term),
                MedicalTest.test_name.ilike(search_term)
            )
        )
    
    # Apply filters if provided
    if patient_id:
        query = query.filter(MedicalTest.patient_id == patient_id)
    if test_date:
        date_obj = datetime.strptime(test_date, '%Y-%m-%d')
        query = query.filter(func.date(MedicalTest.test_date) == date_obj.date())
    
    # Get results ordered by test date
    tests_list = query.order_by(MedicalTest.test_date.desc()).all()
    
    # Get all patients for the filter dropdown
    patients = Patient.query.all()
    
    return render_template('medical_tests/index.html', 
                          medical_tests=tests_list, 
                          patients=patients,
                          search_query=search_query)

@medical_tests_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if request.method == 'POST':
        patient_id = request.form.get('patient_id')
        test_name = request.form.get('test_name')
        test_date = datetime.strptime(request.form.get('test_date'), '%Y-%m-%d')
        results = request.form.get('results')
        doctor_notes = request.form.get('doctor_notes')
        
        new_test = MedicalTest(
            patient_id=patient_id,
            test_name=test_name,
            test_date=test_date,
            results=results,
            doctor_notes=doctor_notes
        )
        
        db.session.add(new_test)
        db.session.commit()
        
        flash('تمت إضافة الفحص الطبي بنجاح!', 'success')
        return redirect(url_for('medical_tests.index'))
    
    patients_list = Patient.query.all()
    return render_template('medical_tests/add.html', patients=patients_list)

@medical_tests_bp.route('/view/<int:test_id>')
@login_required
def view(test_id):
    """عرض تفاصيل الفحص الطبي"""
    test = MedicalTest.query.get_or_404(test_id)
    return render_template('medical_tests/view.html', test=test)

# Add this after the view route

@medical_tests_bp.route('/edit/<int:test_id>', methods=['GET', 'POST'])
@login_required
def edit(test_id):
    """تعديل بيانات الفحص الطبي"""
    test = MedicalTest.query.get_or_404(test_id)
    
    if request.method == 'POST':
        try:
            # Update test data
            test.patient_id = request.form.get('patient_id')
            test.test_name = request.form.get('test_name')
            test_date_str = request.form.get('test_date')
            test.test_date = datetime.strptime(test_date_str, '%Y-%m-%d') if test_date_str else test.test_date
            test.results = request.form.get('results')
            test.doctor_notes = request.form.get('doctor_notes')
            
            db.session.commit()
            flash('تم تحديث الفحص الطبي بنجاح', 'success')
            return redirect(url_for('medical_tests.view', test_id=test.id))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الفحص الطبي: {str(e)}', 'danger')
    
    # Get all patients for the form
    patients = Patient.query.all()
    return render_template('medical_tests/edit.html', test=test, patients=patients)

# Add this after the edit route

@medical_tests_bp.route('/delete/<int:test_id>')
@login_required
def delete(test_id):
    """حذف الفحص الطبي"""
    test = MedicalTest.query.get_or_404(test_id)
    
    try:
        db.session.delete(test)
        db.session.commit()
        flash('تم حذف الفحص الطبي بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الفحص الطبي: {str(e)}', 'danger')
    
    return redirect(url_for('medical_tests.index'))


@medical_tests_bp.route('/api/medical_tests/search')
@login_required
def search_medical_tests():
    search_term = request.args.get('term', '')
    options = json.loads(request.args.get('options', '{}'))
    
    if not search_term:
        return jsonify([])
    
    # Build query based on search options
    query = db.session.query(MedicalTest).join(Patient)
    
    filters = []
    
    if options.get('byName'):
        filters.append(Patient.name.ilike(f'%{search_term}%'))
    
    if options.get('byId'):
        if search_term.isdigit():
            filters.append(Patient.id == int(search_term))
    
    if options.get('byPhone'):
        filters.append(Patient.phone.ilike(f'%{search_term}%'))
    
    if options.get('byTestName'):
        filters.append(MedicalTest.test_name.ilike(f'%{search_term}%'))
    
    # Add this after the view route

    @medical_tests_bp.route('/api/patients/search')
    @login_required
    def search_patients():
        search_term = request.args.get('term', '')
        
        if not search_term or len(search_term) < 2:
            return jsonify([])
        
        # Search for patients by name, ID, or phone
        patients = Patient.query.filter(
            or_(
                Patient.name.ilike(f'%{search_term}%'),
                Patient.phone.ilike(f'%{search_term}%'),
                Patient.id.in_([int(search_term)]) if search_term.isdigit() else False
            )
        ).limit(10).all()
        
        # Format results
        results = []
        for patient in patients:
            results.append({
                'id': patient.id,
                'name': patient.name,
                'phone': patient.phone or 'لا يوجد',
                'value': patient.name,  # For autocomplete
                'label': f"{patient.name} (#{patient.id})"  # For display
            })
        
        return jsonify(results)
    
    # Apply filters with OR logic
    if filters:
        query = query.filter(or_(*filters))
    
    # Limit results
    tests = query.order_by(MedicalTest.test_date.desc()).limit(50).all()
    
    # Format results
    results = []
    for test in tests:
        results.append({
            'id': test.id,
            'patient_name': test.patient.name,
            'patient_id': test.patient.id,
            'test_name': test.test_name,
            'test_date': test.test_date.strftime('%Y-%m-%d')
        })
    
    return jsonify(results)