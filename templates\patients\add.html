{% extends "base.html" %}

{% block title %}إضافة مريض جديد{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">بيانات المريض الجديد</h5>
    </div>
    <div class="card-body">
        <!-- Change the form action from url_for('add_patient') to url_for('patients.add') -->
        <form method="POST" action="{{ url_for('patients.add') }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="birth_date" class="form-label">تاريخ الميلاد <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="gender" class="form-label">الجنس <span class="text-danger">*</span></label>
                    <select class="form-select" id="gender" name="gender" required>
                        <option value="" selected disabled>اختر الجنس</option>
                        <option value="ذكر">ذكر</option>
                        <option value="أنثى">أنثى</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="blood_type" class="form-label">فصيلة الدم</label>
                    <select class="form-select" id="blood_type" name="blood_type">
                        <option value="" selected disabled>اختر فصيلة الدم</option>
                        <option value="A+">A+</option>
                        <option value="A-">A-</option>
                        <option value="B+">B+</option>
                        <option value="B-">B-</option>
                        <option value="AB+">AB+</option>
                        <option value="AB-">AB-</option>
                        <option value="O+">O+</option>
                        <option value="O-">O-</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="phone" name="phone">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" name="email">
                </div>
                <div class="col-md-12 mb-3">
                    <label for="address" class="form-label">العنوان</label>
                    <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="medical_history" class="form-label">التاريخ المرضي</label>
                    <textarea class="form-control" id="medical_history" name="medical_history" rows="3"></textarea>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="allergies" class="form-label">الحساسية تجاه الأدوية</label>
                    <textarea class="form-control" id="allergies" name="allergies" rows="2"></textarea>
                </div>
            </div>
            <div class="d-flex justify-content-between mt-3">
                <!-- Change url_for('patients') to url_for('patients.index') -->
                <a href="{{ url_for('patients.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ البيانات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}