{% extends "base.html" %}

{% block page_title %}إضافة موعد جديد{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">بيانات الموعد الجديد</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('appointments.add') }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="patient_id" class="form-label">المريض <span class="text-danger">*</span></label>
                    <div class="input-group mb-2">
                        <span class="input-group-text bg-light"><i class="fas fa-search text-muted"></i></span>
                        <input type="text" class="form-control" id="patientSearch" placeholder="ابحث عن المريض بالاسم أو رقم الهاتف أو رقم المريض...">
                        <button class="btn btn-outline-secondary" type="button" id="clearPatientSearch">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div id="patientSearchResults" class="position-absolute bg-white shadow rounded p-0 d-none" 
                         style="z-index: 1000; width: calc(50% - 2rem); max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6;"></div>
                    <select class="form-select" id="patient_id" name="patient_id" required>
                        <option value="" selected disabled>اختر المريض</option>
                        {% for patient in patients %}
                        <option value="{{ patient.id }}">{{ patient.name }}</option>
                        {% endfor %}
                    </select>
                    <small class="text-muted mt-1 d-block">اكتب حرفين على الأقل للبحث عن المريض</small>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="doctor_id" class="form-label">الطبيب <span class="text-danger">*</span></label>
                    <select class="form-select" id="doctor_id" name="doctor_id" required>
                        <option value="" selected disabled>اختر الطبيب</option>
                        {% for doctor in doctors %}
                        <option value="{{ doctor.id }}">{{ doctor.name }} - {{ doctor.specialization }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="appointment_date" class="form-label">تاريخ الموعد <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="appointment_date" name="appointment_date" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="appointment_time" class="form-label">وقت الموعد <span class="text-danger">*</span></label>
                    <input type="time" class="form-control" id="appointment_time" name="appointment_time" required>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="status" class="form-label">حالة الموعد</label>
                    <select class="form-select" id="status" name="status">
                        <option value="مؤكد" selected>مؤكد</option>
                        <option value="ملغي">ملغي</option>
                        <option value="منجز">منجز</option>
                    </select>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('appointments.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ الموعد
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Patient search functionality
        const patientSearchInput = document.getElementById('patientSearch');
        const patientSearchResults = document.getElementById('patientSearchResults');
        const patientSelect = document.getElementById('patient_id');
        const clearPatientSearchBtn = document.getElementById('clearPatientSearch');
        
        // Patient data for search
        const patients = [
            {% for patient in patients %}
                {
                    id: {{ patient.id }},
                    name: "{{ patient.name }}",
                    phone: "{{ patient.phone or '' }}",
                    email: "{{ patient.email or '' }}",
                    gender: "{{ patient.gender or '' }}",
                    birthDate: "{{ patient.birth_date.strftime('%Y-%m-%d') if patient.birth_date else '' }}"
                },
            {% endfor %}
        ];
        
        // Search functionality with advanced features
        patientSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim().toLowerCase();
            if (searchTerm.length < 2) {
                patientSearchResults.classList.add('d-none');
                return;
            }
            
            // Advanced search with multiple criteria and scoring
            const matches = patients.map(patient => {
                let score = 0;
                
                // Name match (highest priority)
                if (patient.name.toLowerCase().includes(searchTerm)) {
                    score += 10;
                    // Exact match or starts with gets higher score
                    if (patient.name.toLowerCase() === searchTerm) {
                        score += 5;
                    } else if (patient.name.toLowerCase().startsWith(searchTerm)) {
                        score += 3;
                    }
                }
                
                // Phone match
                if (patient.phone && patient.phone.includes(searchTerm)) {
                    score += 8;
                }
                
                // Patient ID match (direct match with the ID number)
                if (patient.id.toString() === searchTerm) {
                    score += 15; // Highest priority for exact ID match
                } else if (patient.id.toString().includes(searchTerm)) {
                    score += 12; // High priority for partial ID match
                }
                
                // Email match
                if (patient.email && patient.email.toLowerCase().includes(searchTerm)) {
                    score += 5;
                }
                
                return { patient, score };
            }).filter(item => item.score > 0)
              .sort((a, b) => b.score - a.score);
            
            if (matches.length > 0) {
                // Calculate age if birth date is available
                const calculateAge = (birthDate) => {
                    if (!birthDate) return '';
                    const today = new Date();
                    const birth = new Date(birthDate);
                    let age = today.getFullYear() - birth.getFullYear();
                    const monthDiff = today.getMonth() - birth.getMonth();
                    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                        age--;
                    }
                    return age;
                };
                
                patientSearchResults.innerHTML = matches.map(({ patient }) => {
                    const age = calculateAge(patient.birthDate);
                    return `<div class="search-item p-3 border-bottom" data-id="${patient.id}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="fw-bold"><i class="fas fa-user-circle me-2 text-primary"></i>${patient.name}</div>
                            <div class="badge ${patient.gender === 'ذكر' ? 'bg-primary' : 'bg-danger'} rounded-pill">
                                <i class="fas fa-${patient.gender === 'ذكر' ? 'mars' : 'venus'} me-1"></i>${patient.gender || 'غير محدد'}
                            </div>
                        </div>
                        <div class="small text-muted d-flex flex-wrap mt-1">
                            <div class="me-3"><i class="fas fa-id-card me-1 text-dark"></i>رقم المريض: ${patient.id}</div>
                            ${patient.phone ? `<div class="me-3"><i class="fas fa-phone-alt me-1 text-success"></i>${patient.phone}</div>` : ''}
                            ${age ? `<div class="me-3"><i class="fas fa-birthday-cake me-1 text-info"></i>${age} سنة</div>` : ''}
                            ${patient.email ? `<div class="me-3"><i class="fas fa-envelope me-1 text-secondary"></i>${patient.email}</div>` : ''}
                        </div>
                    </div>`;
                }).join('');
                
                patientSearchResults.classList.remove('d-none');
                
                // Add click event to search results
                document.querySelectorAll('.search-item').forEach(item => {
                    item.addEventListener('click', function() {
                        const patientId = this.dataset.id;
                        
                        // Set the select value
                        patientSelect.value = patientId;
                        
                        // Update the search input with the selected patient name
                        const selectedPatient = patients.find(p => p.id == patientId);
                        patientSearchInput.value = selectedPatient.name;
                        
                        // Hide the results
                        patientSearchResults.classList.add('d-none');
                        
                        // Trigger change event on select to update any dependent fields
                        const event = new Event('change');
                        patientSelect.dispatchEvent(event);
                    });
                });
            } else {
                patientSearchResults.innerHTML = '<div class="p-2">لا توجد نتائج</div>';
                patientSearchResults.classList.remove('d-none');
            }
        });
        
        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!patientSearchInput.contains(e.target) && !patientSearchResults.contains(e.target)) {
                patientSearchResults.classList.add('d-none');
            }
        });
        
        // Clear search button
        clearPatientSearchBtn.addEventListener('click', function() {
            patientSearchInput.value = '';
            patientSelect.selectedIndex = 0;
            patientSearchResults.classList.add('d-none');
        });
        
        // Update search input when select changes
        patientSelect.addEventListener('change', function() {
            const selectedPatient = patients.find(p => p.id == this.value);
            if (selectedPatient) {
                patientSearchInput.value = selectedPatient.name;
            } else {
                patientSearchInput.value = '';
            }
        });
        
        // Initialize search input with selected patient if any
        if (patientSelect.value) {
            const selectedPatient = patients.find(p => p.id == patientSelect.value);
            if (selectedPatient) {
                patientSearchInput.value = selectedPatient.name;
            }
        }
        
        // Add keyboard navigation for search results
        patientSearchInput.addEventListener('keydown', function(e) {
            if (patientSearchResults.classList.contains('d-none')) return;
            
            const items = patientSearchResults.querySelectorAll('.search-item');
            if (!items.length) return;
            
            let activeItem = patientSearchResults.querySelector('.search-item.active');
            let activeIndex = -1;
            
            if (activeItem) {
                activeIndex = Array.from(items).indexOf(activeItem);
            }
            
            // Down arrow
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                if (activeItem) activeItem.classList.remove('active');
                activeIndex = (activeIndex + 1) % items.length;
                items[activeIndex].classList.add('active');
                items[activeIndex].scrollIntoView({ block: 'nearest' });
            }
            
            // Up arrow
            else if (e.key === 'ArrowUp') {
                e.preventDefault();
                if (activeItem) activeItem.classList.remove('active');
                activeIndex = (activeIndex - 1 + items.length) % items.length;
                items[activeIndex].classList.add('active');
                items[activeIndex].scrollIntoView({ block: 'nearest' });
            }
            
            // Enter key
            else if (e.key === 'Enter' && activeItem) {
                e.preventDefault();
                activeItem.click();
            }
        });
    });
</script>

<style>
    .search-item {
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .search-item:hover, .search-item.active {
        background-color: #f0f7ff;
        border-right: 3px solid #0d6efd;
    }
    .search-item:last-child {
        border-bottom: none !important;
    }
    .hover-bg-light:hover {
        background-color: #f8f9fa;
    }
    #patientSearchResults {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-radius: 0.375rem;
    }
    #patientSearchResults:empty {
        display: none !important;
    }
    #patientSearch:focus {
        box-shadow: none;
        border-color: #86b7fe;
    }
</style>
{% endblock %}