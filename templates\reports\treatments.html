{% extends "base.html" %}

{% block page_title %}تقرير العلاجات{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير العلاجات</h5>
        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-right me-1"></i> رجوع
        </a>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">إجمالي العلاجات</h5>
                        <h2 class="display-4">{{ total_treatments }}</h2>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">العلاجات الأكثر وصفاً</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="medicationsChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">توزيع العلاجات حسب الأطباء</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="doctorChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">العلاجات الأكثر وصفاً</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>اسم العلاج</th>
                                        <th>عدد مرات الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for medication, count in common_medications %}
                                    <tr>
                                        <td>{{ medication }}</td>
                                        <td>{{ count }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // بيانات العلاجات الأكثر وصفاً
    var medicationsData = {
        labels: [{% for medication, count in common_medications[:5] %}'{{ medication }}',{% endfor %}],
        datasets: [{
            label: 'عدد مرات الوصف',
            data: [{% for medication, count in common_medications[:5] %}{{ count }},{% endfor %}],
            backgroundColor: [
                'rgba(255, 99, 132, 0.6)',
                'rgba(54, 162, 235, 0.6)',
                'rgba(255, 206, 86, 0.6)',
                'rgba(75, 192, 192, 0.6)',
                'rgba(153, 102, 255, 0.6)'
            ],
            borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)'
            ],
            borderWidth: 1
        }]
    };
    
    // رسم مخطط العلاجات الأكثر وصفاً
    var medicationsCtx = document.getElementById('medicationsChart').getContext('2d');
    var medicationsChart = new Chart(medicationsCtx, {
        type: 'bar',
        data: medicationsData,
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // بيانات توزيع الأطباء
    var doctorData = {
        labels: [{% for doctor, count in doctor_distribution %}'{{ doctor }}',{% endfor %}],
        datasets: [{
            label: 'عدد العلاجات',
            data: [{% for doctor, count in doctor_distribution %}{{ count }},{% endfor %}],
            backgroundColor: [
                '#36a2eb', '#ff6384', '#4bc0c0', '#ffcd56', '#9966ff', '#ff9f40',
                '#c9cbcf', '#7ccc63', '#fb8072', '#80b1d3'
            ],
            hoverOffset: 4
        }]
    };
    
    // رسم مخطط توزيع الأطباء
    var doctorCtx = document.getElementById('doctorChart').getContext('2d');
    var doctorChart = new Chart(doctorCtx, {
        type: 'pie',
        data: doctorData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
});
</script>
{% endblock %}