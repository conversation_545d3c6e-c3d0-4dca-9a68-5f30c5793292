{% extends "base.html" %}

{% block page_title %}إدارة المستخدمين{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة المستخدمين</h5>
        <a href="{{ url_for('add_user') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة مستخدم جديد
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المستخدم</th>
                        <th>الدور</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if users %}
                        {% for user in users %}
                        <tr>
                            <td>{{ user.id }}</td>
                            <td>{{ user.username }}</td>
                            <td>{{ user.role }}</td>
                            <td>
                                <a href="{{ url_for('edit_user', user_id=user.id) }}" class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></a>
                                <a href="{{ url_for('change_user_password', user_id=user.id) }}" class="btn btn-sm btn-outline-secondary"><i class="fas fa-key"></i></a>
                                {% if user.id != current_user.id %}
                                <a href="{{ url_for('delete_user', user_id=user.id) }}" class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="4" class="text-center">لا يوجد مستخدمين</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}