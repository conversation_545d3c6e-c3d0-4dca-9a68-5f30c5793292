"""Add login logs table

Revision ID: 02d8eb2d6d81
Revises: 
Create Date: 2025-03-25 20:22:18.756981

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '02d8eb2d6d81'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('login_log')
    with op.batch_alter_table('doctors', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'], ondelete='SET NULL')

    with op.batch_alter_table('invoices', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.alter_column('doctor_id',
               existing_type=sa.INTEGER(),
               nullable=False)

    with op.batch_alter_table('login_logs', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.alter_column('ip_address',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=45),
               existing_nullable=True)

    with op.batch_alter_table('treatments', schema=None) as batch_op:
        batch_op.drop_column('duration')
        batch_op.drop_column('additional_instructions')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('treatments', schema=None) as batch_op:
        batch_op.add_column(sa.Column('additional_instructions', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('duration', sa.VARCHAR(length=50), nullable=True))

    with op.batch_alter_table('login_logs', schema=None) as batch_op:
        batch_op.alter_column('ip_address',
               existing_type=sa.String(length=45),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('invoices', schema=None) as batch_op:
        batch_op.alter_column('doctor_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('doctors', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    op.create_table('login_log',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('user_id', sa.INTEGER(), nullable=False),
    sa.Column('action', sa.VARCHAR(length=20), nullable=False),
    sa.Column('timestamp', sa.DATETIME(), nullable=True),
    sa.Column('ip_address', sa.VARCHAR(length=50), nullable=True),
    sa.Column('user_agent', sa.VARCHAR(length=255), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###
