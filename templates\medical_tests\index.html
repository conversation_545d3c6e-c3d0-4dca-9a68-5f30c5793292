{% extends "base.html" %}

{% block page_title %}إدارة الفحوصات الطبية{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">الفحوصات الطبية</h5>
        <a href="{{ url_for('medical_tests.add') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة فحص جديد
        </a>
    </div>
    <div class="card-body">
        <!-- Advanced Search Box -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="search-container">
                    <form action="{{ url_for('medical_tests.index') }}" method="get" class="row g-3">
                        <div class="col-md-5">
                            <div class="form-group">
                                <label for="patientSearch" class="form-label">بحث عن مريض</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="patientSearch" name="search" 
                                           placeholder="ابحث بالاسم أو رقم الهاتف أو رقم المريض..." 
                                           value="{{ search_query }}">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="patient" class="form-label">تصفية حسب المريض</label>
                                <select class="form-select" id="patient" name="patient">
                                    <option value="">جميع المرضى</option>
                                    {% for patient in patients %}
                                    <option value="{{ patient.id }}" {% if request.args.get('patient')|int == patient.id %}selected{% endif %}>
                                        {{ patient.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="test_date" class="form-label">تاريخ الفحص</label>
                                <input type="date" class="form-control" id="test_date" name="test_date" value="{{ request.args.get('test_date', '') }}">
                            </div>
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="button" class="btn btn-secondary" id="resetFilters">
                                <i class="fas fa-undo"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Rest of your table content -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المريض</th>
                        <th>اسم الفحص</th>
                        <th>تاريخ الفحص</th>
                        <th>النتائج</th>
                        <th>ملاحظات الطبيب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if medical_tests %}
                        {% for test in medical_tests %}
                        <tr>
                            <td>{{ test.id }}</td>
                            <td>{{ test.patient.name }}</td>
                            <td>{{ test.test_name }}</td>
                            <td>{{ test.test_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ test.results|truncate(20) if test.results else 'غير متوفر' }}</td>
                            <td>{{ test.doctor_notes|truncate(20) if test.doctor_notes else '-' }}</td>
                            <td>
                                <a href="{{ url_for('medical_tests.view', test_id=test.id) }}" class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></a>
                                <a href="{{ url_for('medical_tests.edit', test_id=test.id) }}" class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></a>
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ test.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                                
                                <!-- Modal for Delete Confirmation -->
                                <div class="modal fade" id="deleteModal{{ test.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ test.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ test.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من حذف الفحص "{{ test.test_name }}" للمريض "{{ test.patient.name }}"؟
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <a href="{{ url_for('medical_tests.delete', test_id=test.id) }}" class="btn btn-danger">حذف</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="text-center">لا توجد فحوصات طبية</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add this script at the end of your template -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize autocomplete for patient search
    $("#patientSearch").autocomplete({
        source: function(request, response) {
            $.ajax({
                url: "{{ url_for('medical_tests.search_medical_tests') }}",  // Changed from search_patients to search_medical_tests
                dataType: "json",
                data: {
                    term: request.term
                },
                success: function(data) {
                    response(data);
                }
            });
        },
        minLength: 2,
        select: function(event, ui) {
            // Set the selected patient ID to the patient filter dropdown
            $("#patient").val(ui.item.id);
            // Submit the form
            $(this).closest('form').submit();
            return false;
        }
    }).autocomplete("instance")._renderItem = function(ul, item) {
        return $("<li>")
            .append("<div class='autocomplete-item'>" + 
                    "<div class='patient-name'>" + item.name + " (#" + item.id + ")</div>" +
                    "<div class='patient-phone'>" + item.phone + "</div>" +
                    "</div>")
            .appendTo(ul);
    };
    
    // Reset filters button
    $("#resetFilters").click(function() {
        window.location.href = "{{ url_for('medical_tests.index') }}";
    });
});
</script>

<style>
.ui-autocomplete {
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 9999;
    direction: rtl;
    text-align: right;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    padding: 5px 0;
}
.ui-autocomplete .ui-menu-item {
    padding: 0;
}
.autocomplete-item {
    padding: 8px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}
.autocomplete-item:hover {
    background-color: #f8f9fa;
}
.patient-name {
    font-weight: bold;
}
.patient-phone {
    font-size: 0.85em;
    color: #6c757d;
}
</style>
{% endblock %}