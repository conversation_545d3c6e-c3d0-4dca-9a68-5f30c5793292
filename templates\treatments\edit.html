{% extends "base.html" %}

{% block page_title %}تعديل العلاج{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تعديل بيانات العلاج</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('treatments.edit', treatment_id=treatment.id) }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="patient_id" class="form-label">المريض <span class="text-danger">*</span></label>
                    <select class="form-select" id="patient_id" name="patient_id" required>
                        <option value="" disabled>اختر المريض</option>
                        {% for patient in patients %}
                        <option value="{{ patient.id }}" {% if patient.id == treatment.patient_id %}selected{% endif %}>{{ patient.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="doctor_id" class="form-label">الطبيب <span class="text-danger">*</span></label>
                    <select class="form-select" id="doctor_id" name="doctor_id" required>
                        <option value="" disabled>اختر الطبيب</option>
                        {% for doctor in doctors %}
                        <option value="{{ doctor.id }}" {% if doctor.id == treatment.doctor_id %}selected{% endif %}>{{ doctor.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="medication_name" class="form-label">اسم الدواء <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="medication_name" name="medication_name" value="{{ treatment.medication_name }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="dosage" class="form-label">الجرعة <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="dosage" name="dosage" value="{{ treatment.dosage }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="frequency" class="form-label">عدد مرات الاستخدام</label>
                    <input type="text" class="form-control" id="frequency" name="frequency" value="{{ treatment.frequency }}" placeholder="مثال: مرتين يومياً">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="start_date" class="form-label">تاريخ البدء <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ treatment.start_date.strftime('%Y-%m-%d') if treatment.start_date else '' }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="end_date" class="form-label">تاريخ الانتهاء</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ treatment.end_date.strftime('%Y-%m-%d') if treatment.end_date else '' }}">
                </div>
                <div class="col-md-12 mb-3">
                    <label for="instructions" class="form-label">تعليمات الاستخدام</label>
                    <textarea class="form-control" id="instructions" name="instructions" rows="3">{{ treatment.instructions }}</textarea>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="notes" class="form-label">ملاحظات إضافية</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ treatment.notes }}</textarea>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('treatments.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}