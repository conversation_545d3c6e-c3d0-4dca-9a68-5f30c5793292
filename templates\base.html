<!DOCTYPE html>
<html lang="ar" dir="rtl" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block page_title %}نظام إدارة العيادة{% endblock %} | نظام إدارة العيادة</title>
    
    <!-- إضافة سكريبت لتطبيق الوضع الليلي قبل تحميل الصفحة لمنع الوميض -->
    <script>
        // استرجاع الوضع المحفوظ وتطبيقه فوراً قبل تحميل الصفحة
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-bs-theme', savedTheme);
        
        // إضافة نمط مؤقت لمنع الوميض
        if (savedTheme === 'dark') {
            document.write('<style>html, body { background-color: #212529 !important; }</style>');
        }
    </script>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme.css') }}">
    
    <style>
        /* منع الوميض عند التنقل بين الصفحات */
        html.transition,
        html.transition *,
        html.transition *:before,
        html.transition *:after {
            transition: all 0s !important;
            transition-delay: 0s !important;
        }
        
        /* باقي الأنماط كما هي */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: #fff;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin-bottom: 0.25rem;
        }
        
        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #0d6efd;
        }
        
        .sidebar .nav-link i {
            margin-left: 0.5rem;
        }
        
        .content {
            padding: 20px;
        }
        
        .navbar-brand {
            font-weight: bold;
        }
        
        @media print {
            .sidebar, .navbar, .no-print {
                display: none !important;
            }
            
            .content {
                margin: 0 !important;
                padding: 0 !important;
            }
        }
        
        /* تنسيق زر تبديل الوضع */
        .theme-toggle {
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    {% if current_user.is_authenticated %}
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4>نظام إدارة العيادة</h4>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        
                        <!-- Only include links to routes that exist -->
                        <li class="nav-item">
                            <a class="nav-link {% if '/patients' in request.path %}active{% endif %}" href="{{ url_for('patients.index') }}">
                                <i class="fas fa-users"></i>
                                إدارة المرضى
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if '/doctors' in request.path %}active{% endif %}" href="{{ url_for('doctors.index') }}">
                                <i class="fas fa-user-md"></i>
                                إدارة الأطباء
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if '/appointments' in request.path %}active{% endif %}" href="{{ url_for('appointments.index') }}">
                                <i class="fas fa-calendar-check"></i>
                                إدارة المواعيد
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if '/medical_tests' in request.path %}active{% endif %}" href="{{ url_for('medical_tests.index') }}">
                                <i class="fas fa-vial"></i>
                                الفحوصات الطبية
                            </a>
                        </li>
                        
                        <!-- Replace the try/except blocks with simple links -->
                        <li class="nav-item">
                            <a class="nav-link {% if '/treatments' in request.path %}active{% endif %}" href="{{ url_for('treatments.index') }}">
                                <i class="fas fa-pills"></i>
                                العلاجات والأدوية
                            </a>
                        </li>
                        
                        <!-- Add this after the treatments menu item in the sidebar -->
                        <li class="nav-item">
                            <a class="nav-link {% if '/invoices' in request.path %}active{% endif %}" href="{{ url_for('invoices.index') }}">
                                <i class="fas fa-file-invoice-dollar"></i>
                                <span>إدارة الفواتير</span>
                            </a>
                        </li>
                        
                        <!-- Add this after the invoices menu item in the sidebar -->
                        <li class="nav-item">
                            <a class="nav-link {% if '/reports' in request.path %}active{% endif %}" href="{{ url_for('reports.index') }}">
                                <i class="fas fa-chart-bar"></i>
                                <span>التقارير والإحصائيات</span>
                            </a>
                        </li>
                        <!-- Remove the endtry tag that's causing the error -->
                        
                        {% if current_user.role == 'مدير' %}
                        <li class="nav-item">
                            <a class="nav-link {% if '/users' in request.path %}active{% endif %}" href="{{ url_for('users') }}">
                                <i class="fas fa-user-cog"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if '/settings' in request.path %}active{% endif %}" href="{{ url_for('settings') }}">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                        {% endif %}
                        
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block header_title %}{{ self.page_title() }}{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <!-- زر تبديل الوضع -->
                        <button class="btn btn-sm btn-outline-secondary theme-toggle me-2" id="theme-toggle">
                            <i class="fas fa-sun" id="theme-icon-light"></i>
                            <i class="fas fa-moon d-none" id="theme-icon-dark"></i>
                            <span id="theme-text">الوضع الليلي</span>
                        </button>
                        
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> {{ current_user.username }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{{ url_for('profile') }}"><i class="fas fa-user-cog me-1"></i> الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Main Content Block -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    {% else %}
    <div class="container">
        <!-- Flash Messages for non-authenticated users -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show mt-3" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Content for non-authenticated users -->
        {% block auth_content %}{% endblock %}
    </div>
    {% endif %}

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    
    <!-- سكريبت تبديل الوضع - استبدل السكريبت الحالي بهذا -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من وجود تفضيل محفوظ
            const currentTheme = localStorage.getItem('theme') || 'light';
            setTheme(currentTheme);
            
            // إضافة مستمع حدث لزر التبديل
            document.getElementById('theme-toggle').addEventListener('click', function() {
                const html = document.documentElement;
                const currentTheme = html.getAttribute('data-bs-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                // إرسال طلب API لتحديث الوضع في الجلسة
                fetch('/api/set-theme', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ theme: newTheme }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        setTheme(newTheme);
                        localStorage.setItem('theme', newTheme);
                        
                        // إعادة تحميل الصفحة لتطبيق التغييرات بشكل كامل
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // تطبيق التغييرات محلياً في حالة فشل الطلب
                    setTheme(newTheme);
                    localStorage.setItem('theme', newTheme);
                });
            });
            
            function setTheme(theme) {
                const html = document.documentElement;
                html.setAttribute('data-bs-theme', theme);
                
                // تحديث أيقونة وتسمية الزر
                if (theme === 'dark') {
                    document.getElementById('theme-icon-light').classList.add('d-none');
                    document.getElementById('theme-icon-dark').classList.remove('d-none');
                    document.getElementById('theme-text').textContent = 'الوضع النهاري';
                    
                    // إضافة لون خلفية للجسم في الوضع الليلي
                    document.body.style.backgroundColor = '#212529';
                } else {
                    document.getElementById('theme-icon-light').classList.remove('d-none');
                    document.getElementById('theme-icon-dark').classList.add('d-none');
                    document.getElementById('theme-text').textContent = 'الوضع الليلي';
                    
                    // إعادة تعيين لون الخلفية في الوضع النهاري
                    document.body.style.backgroundColor = '';
                }
            }
        });
    </script>
    
    <!-- Custom JS -->
    {% block scripts %}{% endblock %}
</body>
</html>