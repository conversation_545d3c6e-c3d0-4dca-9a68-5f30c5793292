/* تصحيحات للوضع الليلي لمنع ظهور أجزاء بالوضع الخاطئ */

/* تطبيق الوضع الليلي على جميع العناصر */
[data-bs-theme="dark"],
[data-bs-theme="dark"] body,
[data-bs-theme="dark"] .container,
[data-bs-theme="dark"] .container-fluid,
[data-bs-theme="dark"] .row,
[data-bs-theme="dark"] .col,
[data-bs-theme="dark"] .card,
[data-bs-theme="dark"] .main-content,
[data-bs-theme="dark"] .sidebar {
    background-color: #212529 !important;
    color: #f8f9fa !important;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* تعديل ألوان الخلفية للعناصر الرئيسية */
[data-bs-theme="dark"] .card {
    background-color: #2c3034 !important;
    border-color: #495057 !important;
}

[data-bs-theme="dark"] .card-header {
    background-color: #343a40 !important;
    border-color: #495057 !important;
}

/* تأكيد تطبيق الوضع الليلي على العناصر المتداخلة */
[data-bs-theme="dark"] .table,
[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select,
[data-bs-theme="dark"] .dropdown-menu,
[data-bs-theme="dark"] .modal-content {
    background-color: #2c3034 !important;
    color: #f8f9fa !important;
    border-color: #495057 !important;
}

/* تعديل ألوان النصوص والروابط */
[data-bs-theme="dark"] a:not(.btn):not(.nav-link) {
    color: #8bb9fe !important;
}

/* تعديل ألوان الأزرار في الوضع الليلي */
[data-bs-theme="dark"] .btn-outline-secondary {
    color: #f8f9fa !important;
    border-color: #6c757d !important;
}

[data-bs-theme="dark"] .btn-outline-secondary:hover {
    background-color: #6c757d !important;
    color: #fff !important;
}

/* تطبيق الوضع النهاري على جميع العناصر */
[data-bs-theme="light"],
[data-bs-theme="light"] body,
[data-bs-theme="light"] .container,
[data-bs-theme="light"] .container-fluid,
[data-bs-theme="light"] .row,
[data-bs-theme="light"] .col,
[data-bs-theme="light"] .card,
[data-bs-theme="light"] .main-content {
    background-color: #f8f9fa !important;
    color: #212529 !important;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* تعديل ألوان الخلفية للعناصر الرئيسية في الوضع النهاري */
[data-bs-theme="light"] .card {
    background-color: #ffffff !important;
    border-color: #dee2e6 !important;
}

[data-bs-theme="light"] .card-header {
    background-color: #f0f0f0 !important;
    border-color: #dee2e6 !important;
}

/* تطبيق !important على جميع الخصائص لضمان تطبيقها */
.dark-mode {
    background-color: #212529 !important;
    color: #f8f9fa !important;
}

.light-mode {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}