from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models import db, Treatment, Patient, Doctor
from datetime import datetime
import sqlalchemy

treatments_bp = Blueprint('treatments', __name__)

@treatments_bp.route('/')
@login_required
def index():
    try:
        # Get search parameter
        search_query = request.args.get('search', '')
        
        # Build the query
        query = Treatment.query
        
        # Apply search filter if provided
        if search_query:
            # Join with Patient model to search in patient fields
            query = query.join(Patient, Treatment.patient_id == Patient.id)
            # Search in multiple fields
            query = query.filter(
                db.or_(
                    Patient.name.ilike(f'%{search_query}%'),
                    Patient.phone.ilike(f'%{search_query}%'),
                    Patient.id == search_query if search_query.isdigit() else False
                )
            )
        
        # Get treatments with filters applied
        treatments = query.order_by(Treatment.id.desc()).all()
        
        # Get all patients and doctors for dropdown lists
        patients = Patient.query.all()
        doctors = Doctor.query.all()
        
        return render_template('treatments/index.html', 
                              treatments=treatments,
                              patients=patients,
                              doctors=doctors)
    except sqlalchemy.exc.OperationalError as e:
        flash('حدث خطأ في قاعدة البيانات. يرجى التأكد من تحديث هيكل قاعدة البيانات.', 'danger')
        print(f"خطأ في قاعدة البيانات: {e}")
        return render_template('treatments/index.html', treatments=[], patients=[], doctors=[])

# باقي الكود يبقى كما هو
@treatments_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if request.method == 'POST':
        try:
            # Get form data
            patient_id = request.form.get('patient_id')
            doctor_id = request.form.get('doctor_id')
            medication_name = request.form.get('medication_name')
            dosage = request.form.get('dosage')
            frequency = request.form.get('frequency')
            start_date_str = request.form.get('start_date')
            end_date_str = request.form.get('end_date')
            instructions = request.form.get('instructions')
            notes = request.form.get('notes')
            
            # Convert date strings to datetime objects
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else None
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d') if end_date_str else None
            
            # Create new treatment
            new_treatment = Treatment(
                patient_id=patient_id,
                doctor_id=doctor_id,
                medication_name=medication_name,
                dosage=dosage,
                frequency=frequency,
                start_date=start_date,
                end_date=end_date,
                instructions=instructions,
                notes=notes,
                created_at=datetime.now()
            )
            
            db.session.add(new_treatment)
            db.session.commit()
            
            flash('تم إضافة العلاج بنجاح', 'success')
            return redirect(url_for('treatments.index'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة العلاج: {str(e)}', 'danger')
            print(f"Error adding treatment: {e}")
    
    # Get all patients and doctors for the dropdown
    patients = Patient.query.all()
    doctors = Doctor.query.all()
    
    return render_template('treatments/add.html', patients=patients, doctors=doctors)

@treatments_bp.route('/view/<int:treatment_id>')
@login_required
def view(treatment_id):
    treatment = Treatment.query.get_or_404(treatment_id)
    return render_template('treatments/view.html', treatment=treatment)

@treatments_bp.route('/edit/<int:treatment_id>', methods=['GET', 'POST'])
@login_required
def edit(treatment_id):
    treatment = Treatment.query.get_or_404(treatment_id)
    
    if request.method == 'POST':
        try:
            # Update treatment data
            treatment.patient_id = request.form.get('patient_id')
            treatment.doctor_id = request.form.get('doctor_id')
            treatment.medication_name = request.form.get('medication_name')
            treatment.dosage = request.form.get('dosage')
            treatment.frequency = request.form.get('frequency')
            
            start_date_str = request.form.get('start_date')
            end_date_str = request.form.get('end_date')
            
            treatment.start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else None
            treatment.end_date = datetime.strptime(end_date_str, '%Y-%m-%d') if end_date_str else None
            
            treatment.instructions = request.form.get('instructions')
            treatment.notes = request.form.get('notes')
            
            db.session.commit()
            
            flash('تم تحديث العلاج بنجاح', 'success')
            return redirect(url_for('treatments.view', treatment_id=treatment.id))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث العلاج: {str(e)}', 'danger')
            print(f"Error updating treatment: {e}")
    
    # Get all patients and doctors for the dropdown
    patients = Patient.query.all()
    doctors = Doctor.query.all()
    
    return render_template('treatments/edit.html', treatment=treatment, patients=patients, doctors=doctors)

@treatments_bp.route('/delete/<int:treatment_id>')
@login_required
def delete(treatment_id):
    treatment = Treatment.query.get_or_404(treatment_id)
    
    try:
        db.session.delete(treatment)
        db.session.commit()
        flash('تم حذف العلاج بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف العلاج: {str(e)}', 'danger')
        print(f"Error deleting treatment: {e}")
    
    return redirect(url_for('treatments.index'))