{% extends "base.html" %}

{% block page_title %}لوحة التحكم{% endblock %}

{% block content %}
<div class="row">
    <!-- إحصائيات سريعة -->
    <div class="col-md-3 mb-4">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">إجمالي المرضى</h6>
                        <h2 class="mt-2 mb-0">{{ stats.total_patients }}</h2>
                    </div>
                    <i class="fas fa-users fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="{{ url_for('patients.index') }}" class="text-white text-decoration-none">عرض التفاصيل</a>
                <i class="fas fa-arrow-left"></i>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">مواعيد اليوم</h6>
                        <h2 class="mt-2 mb-0">{{ stats.today_appointments }}</h2>
                    </div>
                    <i class="fas fa-calendar-check fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="{{ url_for('appointments.index') }}" class="text-white text-decoration-none">عرض التفاصيل</a>
                <i class="fas fa-arrow-left"></i>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">إجمالي الأطباء</h6>
                        <h2 class="mt-2 mb-0">{{ stats.total_doctors }}</h2>
                    </div>
                    <i class="fas fa-user-md fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="{{ url_for('doctors.index') }}" class="text-white text-decoration-none">عرض التفاصيل</a>
                <i class="fas fa-arrow-left"></i>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-danger text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">فواتير غير مدفوعة</h6>
                        <h2 class="mt-2 mb-0">{{ stats.unpaid_bills }}</h2>
                    </div>
                    <i class="fas fa-file-invoice-dollar fa-3x opacity-50"></i>
                </div>
            </div>
            <!-- Replace the billing.index URL with a placeholder -->
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="#" onclick="alert('هذه الميزة قيد التطوير');" class="text-white text-decoration-none">عرض التفاصيل</a>
                <i class="fas fa-arrow-left"></i>
            </div>
        </div>
    </div>
    
    <!-- مواعيد اليوم -->
    <div class="col-md-8 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">مواعيد اليوم</h5>
                <a href="{{ url_for('appointments.index') }}" class="btn btn-sm btn-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المريض</th>
                                <th>الطبيب</th>
                                <th>الوقت</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if today_appointments %}
                                {% for appointment in today_appointments %}
                                <tr>
                                    <td>{{ appointment.patient.name }}</td>
                                    <td>{{ appointment.doctor.name }}</td>
                                    <td>{{ appointment.appointment_datetime.strftime('%H:%M') }}</td>
                                    <td>
                                        {% if appointment.status == 'مؤكد' %}
                                        <span class="badge bg-success">{{ appointment.status }}</span>
                                        {% elif appointment.status == 'ملغي' %}
                                        <span class="badge bg-danger">{{ appointment.status }}</span>
                                        {% else %}
                                        <span class="badge bg-info">{{ appointment.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('appointments.index') }}" class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></a>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-center">لا توجد مواعيد لهذا اليوم</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- آخر المرضى -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">آخر المرضى المسجلين</h5>
                <a href="{{ url_for('patients.index') }}" class="btn btn-sm btn-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    {% if recent_patients %}
                        {% for patient in recent_patients %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <!-- In the recent patients section -->
                            <div>
                                <h6 class="mb-0">{{ patient.name }}</h6>
                                <small class="text-muted">{{ patient.created_at.strftime('%Y-%m-%d') }}</small>
                            </div>
                            <a href="{{ url_for('patients.view', patient_id=patient.id) }}" class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></a>
                        </li>
                        {% endfor %}
                    {% else %}
                        <li class="list-group-item text-center">لا يوجد مرضى مسجلين</li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}