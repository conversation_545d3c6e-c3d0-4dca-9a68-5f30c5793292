{% extends "base.html" %}

{% block page_title %}تقرير إيرادات الأطباء{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير إيرادات الأطباء</h5>
        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-right me-1"></i> رجوع
        </a>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">إجمالي إيرادات العيادة</h5>
                        <h2 class="display-4">{{ total_clinic_revenue|round(2) }} د.ل</h2>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="table-responsive mt-4">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الطبيب</th>
                        <th>التخصص</th>
                        <th>عدد المواعيد المدفوعة</th>
                        <th>إجمالي الإيرادات</th>
                        <th>النسبة من إجمالي الإيرادات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in doctors_data %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ item.doctor.name }}</td>
                        <td>{{ item.doctor.specialization }}</td>
                        <td>{{ item.paid_appointments }}</td>
                        <td>{{ item.total_revenue|round(2) }}د.ل</td>
                        <td>
                            {% if total_clinic_revenue > 0 %}
                                {{ ((item.total_revenue / total_clinic_revenue) * 100)|round(1) }}%
                            {% else %}
                                0%
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">توزيع الإيرادات حسب الأطباء</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="doctorsRevenueChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chart for doctors revenue
    var doctorsRevenueCtx = document.getElementById('doctorsRevenueChart').getContext('2d');
    var doctorsRevenueChart = new Chart(doctorsRevenueCtx, {
        type: 'pie',
        data: {
            labels: [{% for item in doctors_data %}'{{ item.doctor.name }}',{% endfor %}],
            datasets: [{
                data: [{% for item in doctors_data %}{{ item.total_revenue }},{% endfor %}],
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                    '#5a5c69', '#6610f2', '#fd7e14', '#20c9a6', '#858796'
                ],
                hoverBackgroundColor: [
                    '#2e59d9', '#17a673', '#2c9faf', '#dda20a', '#be2617',
                    '#3a3b45', '#4d0cc0', '#d96502', '#169b80', '#60616f'
                ],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }],
        },
        options: {
            maintainAspectRatio: false,
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        var dataset = data.datasets[tooltipItem.datasetIndex];
                        var total = dataset.data.reduce(function(previousValue, currentValue) {
                            return previousValue + currentValue;
                        });
                        var currentValue = dataset.data[tooltipItem.index];
                        var percentage = Math.floor(((currentValue/total) * 100)+0.5);
                        return data.labels[tooltipItem.index] + ': ' + currentValue.toFixed(2) + 'د.ل (' + percentage + '%)';
                    }
                }
            },
            legend: {
                position: 'right',
                labels: {
                    fontFamily: 'Tajawal'
                }
            },
            plugins: {
                datalabels: {
                    formatter: (value, ctx) => {
                        let sum = 0;
                        let dataArr = ctx.chart.data.datasets[0].data;
                        dataArr.map(data => {
                            sum += data;
                        });
                        let percentage = (value*100 / sum).toFixed(1)+"%";
                        return percentage;
                    },
                    color: '#fff',
                    font: {
                        weight: 'bold',
                        size: 12
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}