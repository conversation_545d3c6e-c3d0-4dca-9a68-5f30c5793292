{% extends "base.html" %}

{% block page_title %}تعديل الفحص الطبي{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تعديل بيانات الفحص الطبي</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('medical_tests.edit', test_id=test.id) }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="patient_id" class="form-label">المريض <span class="text-danger">*</span></label>
                    <select class="form-select" id="patient_id" name="patient_id" required>
                        <option value="" disabled>اختر المريض</option>
                        {% for patient in patients %}
                        <option value="{{ patient.id }}" {% if patient.id == test.patient_id %}selected{% endif %}>{{ patient.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="test_name" class="form-label">اسم الفحص <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="test_name" name="test_name" value="{{ test.test_name }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="test_date" class="form-label">تاريخ الفحص <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="test_date" name="test_date" value="{{ test.test_date.strftime('%Y-%m-%d') }}" required>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="results" class="form-label">نتائج الفحص</label>
                    <textarea class="form-control" id="results" name="results" rows="3">{{ test.results or '' }}</textarea>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="doctor_notes" class="form-label">ملاحظات الطبيب</label>
                    <textarea class="form-control" id="doctor_notes" name="doctor_notes" rows="3">{{ test.doctor_notes or '' }}</textarea>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('medical_tests.view', test_id=test.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}