{% extends "base.html" %}

{% block page_title %}تقرير مخصص - الفواتير{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير الفواتير المخصص</h5>
        <div>
            <a href="{{ url_for('reports.custom_report') }}" class="btn btn-secondary btn-sm me-2">
                <i class="fas fa-edit me-1"></i> تعديل التقرير
            </a>
            <a href="{{ url_for('reports.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h5>معلومات التقرير:</h5>
            <p>
                <strong>نوع التقرير:</strong> تقرير الفواتير<br>
                <strong>الفترة:</strong> 
                {% if start_date %}من {{ start_date }}{% endif %}
                {% if end_date %} إلى {{ end_date }}{% endif %}
                {% if not start_date and not end_date %}كل الفترات{% endif %}
            </p>
        </div>
        
        <div class="d-flex justify-content-between mb-3">
            <div>
                <h5>عدد الفواتير: {{ results|length }}</h5>
                <h5>إجمالي المبالغ: {{ results|sum(attribute='total_amount')|round(2) }} ر.س</h5>
            </div>
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> طباعة التقرير
            </button>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المريض</th>
                        <th>تاريخ الفاتورة</th>
                        <th>المبلغ</th>
                        <th>الضريبة</th>
                        <th>الخصم</th>
                        <th>الإجمالي</th>
                        <th>الحالة</th>
                        <th>طريقة الدفع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in results %}
                    <tr>
                        <td>
                            <a href="{{ url_for('invoices.view', invoice_id=invoice.id) }}">
                                {{ invoice.id }}
                            </a>
                        </td>
                        <td>
                            <a href="{{ url_for('patients.view', patient_id=invoice.patient.id) }}">
                                {{ invoice.patient.name }}
                            </a>
                        </td>
                        <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ invoice.amount|round(2) }} ر.س</td>
                        <td>{{ invoice.tax|round(2) }} ر.س</td>
                        <td>{{ invoice.discount|round(2) }} ر.س</td>
                        <td>{{ invoice.total_amount|round(2) }} ر.س</td>
                        <td>
                            {% if invoice.status == 'مدفوعة' %}
                                <span class="badge bg-success">{{ invoice.status }}</span>
                            {% elif invoice.status == 'غير مدفوعة' %}
                                <span class="badge bg-danger">{{ invoice.status }}</span>
                            {% else %}
                                <span class="badge bg-warning">{{ invoice.status }}</span>
                            {% endif %}
                        </td>
                        <td>{{ invoice.payment_method }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-primary">
                        <th colspan="6" class="text-end">الإجمالي:</th>
                        <th>{{ results|sum(attribute='total_amount')|round(2) }} ر.س</th>
                        <th colspan="2"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
{% endblock %}