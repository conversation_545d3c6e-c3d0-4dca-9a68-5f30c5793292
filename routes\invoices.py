from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models import db, Patient, Appointment, Treatment, Doctor  # Added Doctor import here
from datetime import datetime, timedelta
import sqlalchemy

# We'll define the Invoice model here temporarily if it doesn't exist in models.py
# Add doctor_id to the Invoice model if it doesn't exist
try:
    from models import Invoice
except ImportError:
    from sqlalchemy import Column, Integer, Float, String, DateTime, ForeignKey, Text
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import relationship
    
    Base = declarative_base()
    
    class Invoice(db.Model):
        __tablename__ = 'invoices'
        
        id = db.Column(db.Integer, primary_key=True)
        patient_id = db.Column(db.Integer, db.ForeignKey('patients.id'), nullable=False)
        doctor_id = db.Column(db.Integer, db.<PERSON>('doctors.id'), nullable=False)  # Add this line
        appointment_id = db.Column(db.Integer, db.<PERSON>ey('appointments.id'), nullable=True)
        treatment_id = db.Column(db.Integer, db.ForeignKey('treatments.id'), nullable=True)
        invoice_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
        due_date = db.Column(db.DateTime, nullable=True)
        amount = db.Column(db.Float, nullable=False)
        tax = db.Column(db.Float, default=0)
        discount = db.Column(db.Float, default=0)
        total_amount = db.Column(db.Float, nullable=False)
        status = db.Column(db.String(20), default='غير مدفوعة')
        payment_method = db.Column(db.String(50), nullable=True)
        notes = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        patient = db.relationship('Patient', backref=db.backref('invoices', lazy=True))
        doctor = db.relationship('Doctor', backref=db.backref('invoices', lazy=True))  # Add this line
        appointment = db.relationship('Appointment', backref=db.backref('invoice', lazy=True, uselist=False))
        treatment = db.relationship('Treatment', backref=db.backref('invoice', lazy=True, uselist=False))

invoices_bp = Blueprint('invoices', __name__)

@invoices_bp.route('/')
@login_required
def index():
    try:
        # Get all invoices
        invoices = Invoice.query.order_by(Invoice.invoice_date.desc()).all()
        
        # Get all patients for filtering
        patients = Patient.query.all()
        
        return render_template('invoices/index.html', 
                              invoices=invoices,
                              patients=patients)
    except sqlalchemy.exc.OperationalError as e:
        flash('حدث خطأ في قاعدة البيانات. يرجى التأكد من تحديث هيكل قاعدة البيانات.', 'danger')
        print(f"Database error: {e}")
        return render_template('invoices/index.html', invoices=[], patients=[])

# Update the add route to include doctors in the context and process doctor_id
@invoices_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if request.method == 'POST':
        try:
            # Get form data
            patient_id = request.form.get('patient_id')
            doctor_id = request.form.get('doctor_id')  # Get doctor_id from form
            appointment_id = request.form.get('appointment_id') or None
            treatment_id = request.form.get('treatment_id') or None
            invoice_date_str = request.form.get('invoice_date')
            due_date_str = request.form.get('due_date')
            amount = float(request.form.get('amount'))
            tax = float(request.form.get('tax') or 0)
            discount = float(request.form.get('discount') or 0)
            status = request.form.get('status')
            payment_method = request.form.get('payment_method')
            notes = request.form.get('notes')
            
            # Convert date strings to datetime objects
            invoice_date = datetime.strptime(invoice_date_str, '%Y-%m-%d') if invoice_date_str else datetime.now()
            due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None
            
            # Calculate total amount
            total_amount = amount + tax - discount
            
            # Create new invoice
            new_invoice = Invoice(
                patient_id=patient_id,
                doctor_id=doctor_id,  # Add doctor_id to the invoice
                appointment_id=appointment_id,
                treatment_id=treatment_id,
                invoice_date=invoice_date,
                due_date=due_date,
                amount=amount,
                tax=tax,
                discount=discount,
                total_amount=total_amount,
                status=status,
                payment_method=payment_method,
                notes=notes
            )
            
            db.session.add(new_invoice)
            db.session.commit()
            
            flash('تم إضافة الفاتورة بنجاح', 'success')
            return redirect(url_for('invoices.index'))
        except Exception as e:
            flash(f'حدث خطأ أثناء إضافة الفاتورة: {str(e)}', 'danger')
            print(f"Error adding invoice: {e}")
            return redirect(url_for('invoices.add'))
    
    # For GET requests, prepare the form
    try:
        # Get all patients
        patients = Patient.query.all()
        
        # Get all doctors - Add this
        doctors = Doctor.query.all()
        
        # Get all appointments
        appointments = Appointment.query.all()
        
        # Get all treatments
        treatments = Treatment.query.all()
        
        # Set default dates
        today = datetime.now().strftime('%Y-%m-%d')
        due_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        
        return render_template('invoices/add.html', 
                              patients=patients,
                              doctors=doctors,  # Pass doctors to the template
                              appointments=appointments,
                              treatments=treatments,
                              today=today,
                              due_date=due_date)
    except Exception as e:
        flash(f'حدث خطأ أثناء تحميل الصفحة: {str(e)}', 'danger')
        print(f"Error loading add invoice page: {e}")
        return redirect(url_for('invoices.index'))

@invoices_bp.route('/view/<int:invoice_id>')
@login_required
def view(invoice_id):
    """عرض تفاصيل الفاتورة"""
    invoice = Invoice.query.get_or_404(invoice_id)
    return render_template('invoices/view.html', invoice=invoice)

@invoices_bp.route('/edit/<int:invoice_id>', methods=['GET', 'POST'])
@login_required
def edit(invoice_id):
    """تعديل بيانات الفاتورة"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if request.method == 'POST':
        try:
            # Get form data
            invoice.patient_id = request.form.get('patient_id')
            invoice.doctor_id = request.form.get('doctor_id')  # Add doctor_id handling
            invoice.appointment_id = request.form.get('appointment_id') or None
            invoice.treatment_id = request.form.get('treatment_id') or None
            invoice_date_str = request.form.get('invoice_date')
            due_date_str = request.form.get('due_date')
            invoice.amount = float(request.form.get('amount'))
            invoice.tax = float(request.form.get('tax') or 0)
            invoice.discount = float(request.form.get('discount') or 0)
            invoice.status = request.form.get('status')
            invoice.payment_method = request.form.get('payment_method')
            invoice.notes = request.form.get('notes')
            
            # Convert date strings to datetime objects
            invoice.invoice_date = datetime.strptime(invoice_date_str, '%Y-%m-%d') if invoice_date_str else datetime.now()
            invoice.due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None
            
            # Calculate total amount
            invoice.total_amount = invoice.amount + invoice.tax - invoice.discount
            
            db.session.commit()
            flash('تم تحديث الفاتورة بنجاح', 'success')
            return redirect(url_for('invoices.view', invoice_id=invoice.id))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الفاتورة: {str(e)}', 'danger')
    
    # Get all patients, appointments, treatments, and doctors for the form
    patients = Patient.query.all()
    doctors = Doctor.query.all()  # Add doctors query
    appointments = Appointment.query.all()
    treatments = Treatment.query.all()
    
    return render_template('invoices/edit.html', 
                          invoice=invoice,
                          patients=patients,
                          doctors=doctors,  # Pass doctors to the template
                          appointments=appointments,
                          treatments=treatments)

@invoices_bp.route('/delete/<int:invoice_id>', methods=['GET', 'POST'])
@login_required
def delete(invoice_id):
    try:
        invoice = Invoice.query.get_or_404(invoice_id)
        
        if request.method == 'POST':
            # Delete the invoice
            db.session.delete(invoice)
            db.session.commit()
            flash('تم حذف الفاتورة بنجاح', 'success')
            return redirect(url_for('invoices.index'))
        
        # For GET requests, show the confirmation page
        return render_template('invoices/delete_confirm.html', invoice=invoice)
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف الفاتورة: {str(e)}', 'danger')
        return redirect(url_for('invoices.index'))

@invoices_bp.route('/print/<int:invoice_id>')
@login_required
def print(invoice_id):
    """طباعة الفاتورة"""
    invoice = Invoice.query.get_or_404(invoice_id)
    return render_template('invoices/print.html', invoice=invoice)