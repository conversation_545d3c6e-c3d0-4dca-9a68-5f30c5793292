{% extends "base.html" %}

{% block title %}تعديل بيانات الطبيب{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تعديل بيانات الطبيب</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('doctors.edit', doctor_id=doctor.id) }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" value="{{ doctor.name }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="specialization" class="form-label">التخصص <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="specialization" name="specialization" value="{{ doctor.specialization }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="phone" name="phone" value="{{ doctor.phone or '' }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" name="email" value="{{ doctor.email or '' }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="percentage" class="form-label">نسبة الطبيب (%)</label>
                    <input type="number" class="form-control" id="percentage" name="percentage" value="{{ doctor.percentage or 50 }}" min="0" max="100">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="experience_years" class="form-label">سنوات الخبرة</label>
                    <input type="number" class="form-control" id="experience_years" name="experience_years" min="0" value="{{ doctor.experience_years or '' }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="work_schedule" class="form-label">مواعيد العمل</label>
                    <textarea class="form-control" id="work_schedule" name="work_schedule" rows="2">{{ doctor.work_schedule or '' }}</textarea>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('doctors.view', doctor_id=doctor.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}