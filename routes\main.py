from flask import Blueprint, render_template
from flask_login import login_required
from models import db, Patient, Doctor, Appointment, Billing
from datetime import datetime

main_bp = Blueprint('main', __name__)

@main_bp.route('/dashboard')
@login_required
def dashboard():
    # إحصائيات سريعة
    stats = {
        'total_patients': Patient.query.count(),
        'total_doctors': Doctor.query.count(),
        'today_appointments': Appointment.query.filter(db.func.date(Appointment.appointment_datetime) == datetime.now().date()).count(),
        'unpaid_bills': Billing.query.filter_by(payment_status='غير مدفوع').count()
    }
    
    # مواعيد اليوم
    today_appointments = Appointment.query.filter(
        db.func.date(Appointment.appointment_datetime) == datetime.now().date()
    ).order_by(Appointment.appointment_datetime).all()
    
    # آخر المرضى المسجلين
    recent_patients = Patient.query.order_by(Patient.created_at.desc()).limit(5).all()
    
    return render_template('dashboard.html', 
                          stats=stats,
                          today_appointments=today_appointments,
                          recent_patients=recent_patients)