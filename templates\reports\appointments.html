{% extends "base.html" %}

{% block page_title %}تقرير المواعيد{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير المواعيد</h5>
        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-right me-1"></i> رجوع
        </a>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">إجمالي المواعيد</h5>
                        <h2 class="display-4">{{ total_appointments }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title">المواعيد المنجزة</h5>
                        <h2 class="display-4">{{ completed_appointments }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <h5 class="card-title">المواعيد الملغاة</h5>
                        <h2 class="display-4">{{ cancelled_appointments }}</h2>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">توزيع المواعيد حسب الأطباء</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="doctorChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">توزيع المواعيد حسب أيام الأسبوع</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="dayChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // بيانات توزيع الأطباء
    var doctorData = {
        labels: [{% for doctor, count in doctor_distribution %}'{{ doctor }}',{% endfor %}],
        datasets: [{
            label: 'عدد المواعيد',
            data: [{% for doctor, count in doctor_distribution %}{{ count }},{% endfor %}],
            backgroundColor: [
                '#36a2eb', '#ff6384', '#4bc0c0', '#ffcd56', '#9966ff', '#ff9f40',
                '#c9cbcf', '#7ccc63', '#fb8072', '#80b1d3'
            ],
            hoverOffset: 4
        }]
    };
    
    // رسم مخطط توزيع الأطباء
    var doctorCtx = document.getElementById('doctorChart').getContext('2d');
    var doctorChart = new Chart(doctorCtx, {
        type: 'pie',
        data: doctorData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
    
    // بيانات توزيع الأيام
    var dayData = {
        labels: [{% for day, count in day_distribution %}'{{ day }}',{% endfor %}],
        datasets: [{
            label: 'عدد المواعيد',
            data: [{% for day, count in day_distribution %}{{ count }},{% endfor %}],
            backgroundColor: '#36a2eb',
            borderColor: '#36a2eb',
            borderWidth: 1
        }]
    };
    
    // رسم مخطط توزيع الأيام
    var dayCtx = document.getElementById('dayChart').getContext('2d');
    var dayChart = new Chart(dayCtx, {
        type: 'bar',
        data: dayData,
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
{% endblock %}