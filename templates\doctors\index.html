{% extends "base.html" %}

{% block title %}إدارة الأطباء{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة الأطباء</h5>
        <a href="{{ url_for('doctors.add') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة طبيب جديد
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>التخصص</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>سنوات الخبرة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if doctors %}
                        {% for doctor in doctors %}
                        <tr>
                            <td>{{ doctor.id }}</td>
                            <td>{{ doctor.name }}</td>
                            <td>{{ doctor.specialization }}</td>
                            <td>{{ doctor.phone }}</td>
                            <td>{{ doctor.email }}</td>
                            <td>{{ doctor.experience_years }}</td>
                            <!-- In the actions column of your doctors table -->
                            <td>
                                <a href="{{ url_for('doctors.view', doctor_id=doctor.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('doctors.edit', doctor_id=doctor.id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('doctors.delete_confirm', doctor_id=doctor.id) }}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash-alt"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="text-center">لا يوجد أطباء مسجلين</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}