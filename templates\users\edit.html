{% extends "base.html" %}

{% block page_title %}تعديل بيانات المستخدم{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تعديل بيانات المستخدم</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('edit_user', user_id=user.id) }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="role" class="form-label">الدور <span class="text-danger">*</span></label>
                    <select class="form-select" id="role" name="role" required>
                        <option value="مدير" {% if user.role == 'مدير' %}selected{% endif %}>مدير</option>
                        <option value="طبيب" {% if user.role == 'طبيب' %}selected{% endif %}>طبيب</option>
                        <option value="موظف استقبال" {% if user.role == 'موظف استقبال' %}selected{% endif %}>موظف استقبال</option>
                    </select>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('users') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}