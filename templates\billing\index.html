{% extends "base.html" %}

{% block page_title %}إدارة الفواتير{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة الفواتير</h5>
        <a href="{{ url_for('add_bill') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة فاتورة جديدة
        </a>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <form method="GET" action="{{ url_for('billing') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="payment_status" class="form-label">حالة الدفع</label>
                    <select class="form-select" id="payment_status" name="payment_status">
                        <option value="">الكل</option>
                        <option value="مدفوع" {% if request.args.get('payment_status') == 'مدفوع' %}selected{% endif %}>مدفوع</option>
                        <option value="غير مدفوع" {% if request.args.get('payment_status') == 'غير مدفوع' %}selected{% endif %}>غير مدفوع</option>
                        <option value="جزئي" {% if request.args.get('payment_status') == 'جزئي' %}selected{% endif %}>جزئي</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.args.get('date_from', '') }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.args.get('date_to', '') }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i> بحث
                    </button>
                    <a href="{{ url_for('billing') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المريض</th>
                        <th>تاريخ الفاتورة</th>
                        <th>المبلغ الإجمالي</th>
                        <th>حالة الدفع</th>
                        <th>الوصف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if bills %}
                        {% for bill in bills %}
                        <tr>
                            <td>{{ bill.id }}</td>
                            <td>{{ bill.patient.name }}</td>
                            <td>{{ bill.bill_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ bill.total_amount }} ريال</td>
                            <td>
                                {% if bill.payment_status == 'مدفوع' %}
                                <span class="badge bg-success">{{ bill.payment_status }}</span>
                                {% elif bill.payment_status == 'غير مدفوع' %}
                                <span class="badge bg-danger">{{ bill.payment_status }}</span>
                                {% else %}
                                <span class="badge bg-warning">{{ bill.payment_status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ bill.description|truncate(20) if bill.description else '-' }}</td>
                            <td>
                                <a href="#" class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></a>
                                <a href="#" class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></a>
                                <a href="#" class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></a>
                                <a href="#" class="btn btn-sm btn-outline-success"><i class="fas fa-print"></i></a>
                            </td>