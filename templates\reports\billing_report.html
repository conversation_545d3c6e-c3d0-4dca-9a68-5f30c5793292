{% extends "base.html" %}

{% block page_title %}تقرير الفواتير{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير الفواتير</h5>
        <div>
            <a href="{{ url_for('reports') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>حالة الدفع:</strong> {{ payment_status if payment_status else 'الكل' }}</p>
                </div>
                <div class="col-md-4">
                    <p><strong>من تاريخ:</strong> {{ date_from if date_from else 'غير محدد' }}</p>
                </div>
                <div class="col-md-4">
                    <p><strong>إلى تاريخ:</strong> {{ date_to if date_to else 'غير محدد' }}</p>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المريض</th>
                        <th>تاريخ الفاتورة</th>
                        <th>المبلغ الإجمالي</th>
                        <th>حالة الدفع</th>
                        <th>الوصف</th>
                    </tr>
                </thead>
                <tbody>
                    {% if bills %}
                        {% for bill in bills %}
                        <tr>
                            <td>{{ bill.id }}</td>
                            <td>{{ bill.patient.name }}</td>
                            <td>{{ bill.bill_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ bill.total_amount }} ريال</td>
                            <td>{{ bill.payment_status }}</td>
                            <td>{{ bill.description }}</td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center">لا توجد فواتير في هذه الفترة</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <div class="mt-3">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>إجمالي عدد الفواتير:</strong> {{ bills|length }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>إجمالي المبالغ:</strong> {{ total_amount }} ريال</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}