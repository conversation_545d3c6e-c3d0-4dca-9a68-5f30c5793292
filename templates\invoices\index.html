{% extends "base.html" %}

{% block page_title %}إدارة الفواتير{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة الفواتير</h5>
        <a href="{{ url_for('invoices.add') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة فاتورة جديدة
        </a>
    </div>
    <div class="card-body">
        <!-- نموذج البحث -->
        <div class="card mb-3">
            <div class="card-header bg-light">
                <h6 class="mb-0"><i class="fas fa-search me-2"></i>بحث متقدم</h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('invoices.index') }}" id="searchForm">
                    <div class="input-group">
                        <input type="text" class="form-control" id="search_query" name="search_query" 
                               placeholder="ابحث عن اسم المريض    " 
                               value="{{ request.args.get('search_query', '') }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                        <a href="{{ url_for('invoices.index') }}" class="btn btn-secondary">
                            <i class="fas fa-redo me-1"></i> إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- عرض الفلاتر النشطة -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <h6 class="mb-0">إجمالي النتائج: <span class="badge bg-primary">{{ invoices|length }}</span></h6>
            </div>
            <!-- عرض الفلاتر النشطة -->
            <div id="activeFilters" class="d-flex flex-wrap">
                {% if request.args.get('search_query') %}
                <span class="badge bg-info me-2 mb-1">
                    نتائج البحث: {{ request.args.get('search_query') }}
                    <a href="{{ url_for('invoices.index') }}" class="text-white ms-1"><i class="fas fa-times"></i></a>
                </span>
                {% endif %}
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المريض</th>
                        <th>الطبيب</th>
                        <th>تاريخ الفاتورة</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>المبلغ</th>
                        <th>الضريبة</th>
                        <th>الخصم</th>
                        <th>المبلغ الإجمالي</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if invoices %}
                        {% for invoice in invoices %}
                        <tr>
                            <td>{{ invoice.id }}</td>
                            <td>{{ invoice.patient.name }}</td>
                            <td>{{ invoice.doctor.name if invoice.doctor else '-' }}</td>
                            <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '-' }}</td>
                            <td>{{ invoice.amount }} د.ل</td>
                            <td>{{ invoice.tax }} د.ل</td>
                            <td>{{ invoice.discount }} د.ل</td>
                            <td>{{ invoice.total_amount }} د.ل</td>
                            <td>
                                {% if invoice.status == 'مدفوعة' %}
                                    <span class="badge bg-success">{{ invoice.status }}</span>
                                {% elif invoice.status == 'غير مدفوعة' %}
                                    <span class="badge bg-danger">{{ invoice.status }}</span>
                                {% else %}
                                    <span class="badge bg-warning">{{ invoice.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('invoices.view', invoice_id=invoice.id) }}" class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></a>
                                <a href="{{ url_for('invoices.edit', invoice_id=invoice.id) }}" class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></a>
                                <a href="{{ url_for('invoices.print', invoice_id=invoice.id) }}" class="btn btn-sm btn-outline-info" target="_blank"><i class="fas fa-print"></i></a>
                                <a href="{{ url_for('invoices.delete', invoice_id=invoice.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الفاتورة؟')"><i class="fas fa-trash"></i></a>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="10" class="text-center">لا توجد فواتير مسجلة</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة خاصية البحث الفوري المحسنة
        const searchInput = document.getElementById('search_query');
        const searchForm = document.getElementById('searchForm');
        const tableRows = document.querySelectorAll('tbody tr');
        let searchTimeout;
        let originalRowData = [];

        // حفظ البيانات الأصلية للصفوف
        tableRows.forEach(row => {
            const rowData = Array.from(row.cells).map(cell => cell.innerHTML);
            originalRowData.push({ row, data: rowData });
        });

        if (searchInput) {
            // تطبيق التركيز التلقائي على حقل البحث
            searchInput.focus();
            
            // إضافة أيقونة البحث داخل حقل البحث
            searchInput.style.backgroundImage = "url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"%23adb5bd\" class=\"bi bi-search\" viewBox=\"0 0 16 16\"><path d=\"M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z\"/></svg>')";
            searchInput.style.backgroundRepeat = "no-repeat";
            searchInput.style.backgroundPosition = "left 10px center";
            searchInput.style.paddingLeft = "35px";
            
            // تنفيذ البحث الفوري المحسن
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = this.value.trim().toLowerCase();
                
                // إضافة تأثير التحميل
                if (searchTerm.length > 0) {
                    searchInput.classList.add('searching');
                } else {
                    searchInput.classList.remove('searching');
                }
                
                // تأخير البحث لتحسين الأداء
                searchTimeout = setTimeout(function() {
                    // إعادة تعيين جميع الصفوف إلى حالتها الأصلية
                    originalRowData.forEach(item => {
                        Array.from(item.row.cells).forEach((cell, index) => {
                            cell.innerHTML = item.data[index];
                        });
                        item.row.classList.remove('highlight-row', 'search-animation');
                        item.row.style.display = '';
                    });
                    
                    // إذا كان حقل البحث فارغًا، أظهر جميع الصفوف
                    if (searchTerm === '') {
                        updateResultCount();
                        return;
                    }
                    
                    let matchCount = 0;
                    
                    // تطبيق البحث على جميع الأعمدة المهمة
                    originalRowData.forEach(item => {
                        const rowText = Array.from(item.row.cells).map(cell => 
                            cell.textContent.toLowerCase()
                        ).join(' ');
                        
                        // البحث في النص الكامل للصف
                        if (rowText.includes(searchTerm)) {
                            matchCount++;
                            item.row.style.display = '';
                            item.row.classList.add('highlight-row', 'search-animation');
                            
                            // تمييز النص المطابق في جميع الخلايا
                            Array.from(item.row.cells).forEach((cell, index) => {
                                const originalText = item.data[index];
                                if (cell.textContent.toLowerCase().includes(searchTerm)) {
                                    try {
                                        const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                                        cell.innerHTML = originalText.replace(regex, '<mark>$1</mark>');
                                    } catch (e) {
                                        console.error('خطأ في التعبير النمطي:', e);
                                    }
                                }
                            });
                        } else {
                            item.row.style.display = 'none';
                        }
                    });
                    
                    // إظهار رسالة إذا لم يتم العثور على نتائج
                    const noResultsRow = document.querySelector('.no-results-row');
                    if (matchCount === 0) {
                        if (!noResultsRow) {
                            const tbody = document.querySelector('tbody');
                            const newRow = document.createElement('tr');
                            newRow.className = 'no-results-row';
                            newRow.innerHTML = `<td colspan="10" class="text-center">لا توجد نتائج مطابقة لـ "${searchTerm}"</td>`;
                            tbody.appendChild(newRow);
                        } else {
                            noResultsRow.style.display = '';
                            noResultsRow.querySelector('td').textContent = `لا توجد نتائج مطابقة لـ "${searchTerm}"`;
                        }
                    } else if (noResultsRow) {
                        noResultsRow.style.display = 'none';
                    }
                    
                    // تحديث عدد النتائج
                    updateResultCount();
                    
                    // إزالة تأثير التحميل
                    searchInput.classList.remove('searching');
                }, 300);
            });
            
            // تقديم النموذج عند الضغط على Enter
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchForm.submit();
                }
            });
            
            // إضافة زر مسح للبحث
            const clearButton = document.createElement('button');
            clearButton.type = 'button';
            clearButton.className = 'search-clear-btn';
            clearButton.innerHTML = '&times;';
            clearButton.style.display = 'none';
            
            // إضافة زر المسح بعد حقل البحث
            searchInput.parentNode.insertBefore(clearButton, searchInput.nextSibling);
            
            // إظهار/إخفاء زر المسح
            searchInput.addEventListener('input', function() {
                clearButton.style.display = this.value.length > 0 ? 'block' : 'none';
            });
            
            // مسح البحث عند النقر على زر المسح
            clearButton.addEventListener('click', function() {
                searchInput.value = '';
                searchInput.dispatchEvent(new Event('input'));
                searchInput.focus();
            });
        }
        
        // تحديث عدد النتائج المعروضة
        function updateResultCount() {
            const visibleRows = document.querySelectorAll('tbody tr:not([style*="display: none"]):not(.no-results-row)');
            const resultCountElement = document.querySelector('.badge.bg-primary');
            if (resultCountElement) {
                resultCountElement.textContent = visibleRows.length;
            }
        }
        
        // إضافة تأثيرات CSS المحسنة للبحث
        const style = document.createElement('style');
        style.textContent = `
            #search_query {
                transition: all 0.3s ease;
                border-radius: 20px;
            }
            #search_query:focus {
                box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
                border-color: #86b7fe;
                border-radius: 20px;
            }
            .highlight-row {
                background-color: rgba(255, 243, 205, 0.5);
                transition: background-color 0.3s;
            }
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            .search-animation {
                animation: fadeIn 0.5s;
            }
            mark {
                background-color: #ffc107;
                padding: 0.1em 0.2em;
                border-radius: 3px;
                color: #000;
            }
            .searching {
                background-size: 20px;
                background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="%230d6efd" class="bi bi-search" viewBox="0 0 16 16"><path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/></svg>') !important;
            }
            .search-clear-btn {
                position: absolute;
                right: 120px;
                top: 50%;
                transform: translateY(-50%);
                background: none;
                border: none;
                color: #6c757d;
                font-size: 18px;
                cursor: pointer;
                z-index: 10;
                width: 24px;
                height: 24px;
                line-height: 1;
                border-radius: 50%;
            }
            .search-clear-btn:hover {
                background-color: #e9ecef;
                color: #495057;
            }
            .input-group {
                position: relative;
            }
        `;
        document.head.appendChild(style);
    });
</script>
{% endblock %}