{% extends "base.html" %}

{% block page_title %}تقرير مخصص - المواعيد{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير المواعيد المخصص</h5>
        <div>
            <a href="{{ url_for('reports.custom_report') }}" class="btn btn-secondary btn-sm me-2">
                <i class="fas fa-edit me-1"></i> تعديل التقرير
            </a>
            <a href="{{ url_for('reports.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h5>معلومات التقرير:</h5>
            <p>
                <strong>نوع التقرير:</strong> تقرير المواعيد<br>
                <strong>الفترة:</strong> 
                {% if start_date %}من {{ start_date }}{% endif %}
                {% if end_date %} إلى {{ end_date }}{% endif %}
                {% if not start_date and not end_date %}كل الفترات{% endif %}
            </p>
        </div>
        
        <div class="d-flex justify-content-between mb-3">
            <h5>عدد المواعيد: {{ results|length }}</h5>
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> طباعة التقرير
            </button>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المريض</th>
                        <th>اسم الطبيب</th>
                        <th>تاريخ الموعد</th>
                        <th>الحالة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for appointment in results %}
                    <tr>
                        <td>{{ appointment.id }}</td>
                        <td>
                            <a href="{{ url_for('patients.view', patient_id=appointment.patient.id) }}">
                                {{ appointment.patient.name }}
                            </a>
                        </td>
                        <td>{{ appointment.doctor.name }}</td>
                        <td>{{ appointment.appointment_datetime.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            {% if appointment.status == 'مؤكد' %}
                                <span class="badge bg-primary">{{ appointment.status }}</span>
                            {% elif appointment.status == 'منجز' %}
                                <span class="badge bg-success">{{ appointment.status }}</span>
                            {% elif appointment.status == 'ملغي' %}
                                <span class="badge bg-danger">{{ appointment.status }}</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ appointment.status }}</span>
                            {% endif %}
                        </td>
                        <td>{{ appointment.notes }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}