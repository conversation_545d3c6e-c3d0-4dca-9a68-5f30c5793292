{% extends "base.html" %}

{% block title %}تعديل بيانات المريض{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تعديل بيانات المريض</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('patients.edit', patient_id=patient.id) }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" value="{{ patient.name }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="birth_date" class="form-label">تاريخ الميلاد <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="birth_date" name="birth_date" value="{{ patient.birth_date.strftime('%Y-%m-%d') }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="gender" class="form-label">الجنس <span class="text-danger">*</span></label>
                    <select class="form-select" id="gender" name="gender" required>
                        <option value="ذكر" {% if patient.gender == 'ذكر' %}selected{% endif %}>ذكر</option>
                        <option value="أنثى" {% if patient.gender == 'أنثى' %}selected{% endif %}>أنثى</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="blood_type" class="form-label">فصيلة الدم</label>
                    <select class="form-select" id="blood_type" name="blood_type">
                        <option value="" {% if not patient.blood_type %}selected{% endif %}>غير محدد</option>
                        <option value="A+" {% if patient.blood_type == 'A+' %}selected{% endif %}>A+</option>
                        <option value="A-" {% if patient.blood_type == 'A-' %}selected{% endif %}>A-</option>
                        <option value="B+" {% if patient.blood_type == 'B+' %}selected{% endif %}>B+</option>
                        <option value="B-" {% if patient.blood_type == 'B-' %}selected{% endif %}>B-</option>
                        <option value="AB+" {% if patient.blood_type == 'AB+' %}selected{% endif %}>AB+</option>
                        <option value="AB-" {% if patient.blood_type == 'AB-' %}selected{% endif %}>AB-</option>
                        <option value="O+" {% if patient.blood_type == 'O+' %}selected{% endif %}>O+</option>
                        <option value="O-" {% if patient.blood_type == 'O-' %}selected{% endif %}>O-</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="phone" name="phone" value="{{ patient.phone or '' }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" name="email" value="{{ patient.email or '' }}">
                </div>
                <div class="col-md-12 mb-3">
                    <label for="address" class="form-label">العنوان</label>
                    <textarea class="form-control" id="address" name="address" rows="2">{{ patient.address or '' }}</textarea>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="medical_history" class="form-label">التاريخ الطبي</label>
                    <textarea class="form-control" id="medical_history" name="medical_history" rows="3">{{ patient.medical_history or '' }}</textarea>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="allergies" class="form-label">الحساسية</label>
                    <textarea class="form-control" id="allergies" name="allergies" rows="2">{{ patient.allergies or '' }}</textarea>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('patients.view', patient_id=patient.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}