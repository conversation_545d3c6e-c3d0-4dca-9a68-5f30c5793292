from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required
from models import db, Appointment, Patient, Doctor
from datetime import datetime

appointments_bp = Blueprint('appointments', __name__)

@appointments_bp.route('/')
@login_required
def index():
    # Get filter parameters
    patient_filter = request.args.get('patient')
    date_filter = request.args.get('date')
    status_filter = request.args.get('status')
    
    # Start with base query
    query = Appointment.query
    
    # Apply filters if provided
    if patient_filter:
        query = query.filter(Appointment.patient_id == patient_filter)
    
    if date_filter:
        query = query.filter(db.func.date(Appointment.appointment_datetime) == date_filter)
    
    if status_filter:
        query = query.filter(Appointment.status == status_filter)
    
    # Get sorted appointments
    appointments = query.order_by(Appointment.appointment_datetime.desc()).all()
    
    # Get all patients for search dropdown
    patients = Patient.query.all()
    
    return render_template('appointments/index.html', 
                          appointments=appointments,
                          patients=patients)

@appointments_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if request.method == 'POST':
        patient_id = request.form.get('patient_id')
        doctor_id = request.form.get('doctor_id')
        appointment_date = request.form.get('appointment_date')
        appointment_time = request.form.get('appointment_time')
        status = request.form.get('status')
        notes = request.form.get('notes')
        
        # دمج التاريخ والوقت
        appointment_datetime = datetime.strptime(f"{appointment_date} {appointment_time}", '%Y-%m-%d %H:%M')
        
        new_appointment = Appointment(
            patient_id=patient_id,
            doctor_id=doctor_id,
            appointment_datetime=appointment_datetime,
            status=status,
            notes=notes
        )
        
        db.session.add(new_appointment)
        db.session.commit()
        
        flash('تمت إضافة الموعد بنجاح!', 'success')
        return redirect(url_for('appointments.index'))
    
    patients_list = Patient.query.all()
    doctors_list = Doctor.query.all()
    
    return render_template('appointments/add.html', 
                          patients=patients_list,
                          doctors=doctors_list)

# Add these routes to your appointments.py file

@appointments_bp.route('/edit/<int:appointment_id>', methods=['GET', 'POST'])
@login_required
def edit(appointment_id):
    appointment = Appointment.query.get_or_404(appointment_id)
    
    if request.method == 'POST':
        patient_id = request.form.get('patient_id')
        doctor_id = request.form.get('doctor_id')
        appointment_date = request.form.get('appointment_date')
        appointment_time = request.form.get('appointment_time')
        status = request.form.get('status')
        notes = request.form.get('notes')
        
        # دمج التاريخ والوقت
        appointment_datetime = datetime.strptime(f"{appointment_date} {appointment_time}", '%Y-%m-%d %H:%M')
        
        appointment.patient_id = patient_id
        appointment.doctor_id = doctor_id
        appointment.appointment_datetime = appointment_datetime
        appointment.status = status
        appointment.notes = notes
        
        db.session.commit()
        
        flash('تم تحديث الموعد بنجاح!', 'success')
        return redirect(url_for('appointments.index'))
    
    patients_list = Patient.query.all()
    doctors_list = Doctor.query.all()
    
    # تنسيق التاريخ والوقت للعرض في النموذج
    appointment_date = appointment.appointment_datetime.strftime('%Y-%m-%d')
    appointment_time = appointment.appointment_datetime.strftime('%H:%M')
    
    return render_template('appointments/edit.html', 
                          appointment=appointment,
                          appointment_date=appointment_date,
                          appointment_time=appointment_time,
                          patients=patients_list,
                          doctors=doctors_list)

@appointments_bp.route('/delete_confirm/<int:appointment_id>')
@login_required
def delete_confirm(appointment_id):
    appointment = Appointment.query.get_or_404(appointment_id)
    return render_template('appointments/delete_confirm.html', appointment=appointment)

# Add this route to handle appointment deletion
@appointments_bp.route('/delete/<int:appointment_id>', methods=['POST'])
@login_required
def delete_appointment(appointment_id):
    # Check if the appointment exists
    appointment = Appointment.query.get_or_404(appointment_id)
    
    try:
        # Delete the appointment
        db.session.delete(appointment)
        db.session.commit()
        flash('تم حذف الموعد بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الموعد: {str(e)}', 'danger')
    
    return redirect(url_for('appointments.index'))

@appointments_bp.route('/details/<int:appointment_id>')
@login_required
def details(appointment_id):
    appointment = Appointment.query.get_or_404(appointment_id)
    return render_template('appointments/details.html', appointment=appointment)