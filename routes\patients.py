from flask import Blueprint, render_template, redirect, url_for, request, flash
from flask_login import login_required
from models import db, Patient
from datetime import datetime
import pandas as pd
import os
import tempfile
from fpdf import FPDF
import io

patients_bp = Blueprint('patients', __name__)

@patients_bp.route('/')
@login_required
def index():
    patients_list = Patient.query.all()
    return render_template('patients/index.html', patients=patients_list)

# Add export to Excel functionality
@patients_bp.route('/export/excel')
@login_required
def export_excel():
    patients = Patient.query.all()
    
    # Create a DataFrame from patients data
    data = []
    for patient in patients:
        data.append({
            'ID': patient.id,
            'الاسم': patient.name,
            'تاريخ الميلاد': patient.birth_date.strftime('%Y-%m-%d') if patient.birth_date else '',
            'الجنس': patient.gender,
            'العنوان': patient.address or '',
            'رقم الهاتف': patient.phone or '',
            'البريد الإلكتروني': patient.email or '',
            'فصيلة الدم': patient.blood_type or '',
            'الحساسية': patient.allergies or '',
            'التاريخ الطبي': patient.medical_history or '',
            'تاريخ التسجيل': patient.created_at.strftime('%Y-%m-%d %H:%M:%S') if patient.created_at else ''
        })
    
    df = pd.DataFrame(data)
    
    # Create a temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
    temp_file.close()
    
    # Write DataFrame to Excel
    df.to_excel(temp_file.name, index=False)
    
    # Send the file
    return send_file(temp_file.name, 
                     as_attachment=True, 
                     download_name='patients_data.xlsx',
                     mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

# Add export to PDF functionality
@patients_bp.route('/export/pdf')
@login_required
def export_pdf():
    patients = Patient.query.all()
    
    try:
        # Use a different PDF library with better Arabic support
        from arabic_reshaper import reshape
        from bidi.algorithm import get_display
        from fpdf import FPDF
        
        # Create PDF with Arabic support
        class ArabicPDF(FPDF):
            def __init__(self):
                super().__init__()
                # Use Arial font which has better Arabic support
                self.add_font('Arial', '', 'c:\\Windows\\Fonts\\arial.ttf', uni=True)
                self.add_font('Arial', 'B', 'c:\\Windows\\Fonts\\arialbd.ttf', uni=True)
            
            def header(self):
                # Set font for header
                self.set_font('Arial', 'B', 15)
                # Process Arabic text
                title = get_display(reshape('قائمة المرضى'))
                # Title
                self.cell(0, 10, title, 0, 1, 'C')
                # Line break
                self.ln(5)
                
            def footer(self):
                # Position at 1.5 cm from bottom
                self.set_y(-15)
                self.set_font('Arial', '', 8)
                # Page number
                page_text = get_display(reshape(f'صفحة {self.page_no()}'))
                self.cell(0, 10, page_text, 0, 0, 'C')
                
            def arabic_cell(self, w, h, txt, border=0, ln=0, align='', fill=False):
                # Process Arabic text
                processed_text = get_display(reshape(txt))
                self.cell(w, h, processed_text, border, ln, align, fill)
        
        # Initialize PDF
        pdf = ArabicPDF()
        pdf.set_auto_page_break(auto=True, margin=15)
        pdf.add_page()
        
        # Set font for content
        pdf.set_font('Arial', 'B', 12)
        
        # Table header with better spacing
        pdf.set_fill_color(230, 230, 230)
        
        # Define column widths for better Arabic text display
        col_widths = [15, 50, 30, 20, 30, 20, 30]
        headers = ['ID', 'الاسم', 'تاريخ الميلاد', 'الجنس', 'رقم الهاتف', 'فصيلة الدم', 'تاريخ التسجيل']
        
        # Draw header with fill
        for i, header in enumerate(headers):
            pdf.arabic_cell(col_widths[i], 10, header, 1, 0, 'C', True)
        pdf.ln()
        
        # Table data with improved Arabic rendering
        pdf.set_font('Arial', '', 11)
        for patient in patients:
            # Convert data to strings and handle None values
            id_str = str(patient.id)
            name = patient.name or ''
            birth_date = patient.birth_date.strftime('%Y-%m-%d') if patient.birth_date else ''
            gender = patient.gender or ''
            phone = patient.phone or ''
            blood_type = patient.blood_type or ''
            created_at = patient.created_at.strftime('%Y-%m-%d') if patient.created_at else ''
            
            # Write data with proper alignment for Arabic
            pdf.cell(col_widths[0], 10, id_str, 1, 0, 'C')
            pdf.arabic_cell(col_widths[1], 10, name, 1, 0, 'R')  # Right align for Arabic names
            pdf.cell(col_widths[2], 10, birth_date, 1, 0, 'C')
            pdf.arabic_cell(col_widths[3], 10, gender, 1, 0, 'R')  # Right align for Arabic text
            pdf.cell(col_widths[4], 10, phone, 1, 0, 'C')
            pdf.arabic_cell(col_widths[5], 10, blood_type, 1, 0, 'C')
            pdf.cell(col_widths[6], 10, created_at, 1, 0, 'C')
            pdf.ln()
        
        # Save PDF to a bytes buffer
        pdf_buffer = io.BytesIO()
        pdf.output(pdf_buffer)
        pdf_buffer.seek(0)
        
        # Send the PDF file
        return send_file(
            pdf_buffer,
            as_attachment=True,
            download_name='patients_data.pdf',
            mimetype='application/pdf'
        )
    
    except Exception as e:
        # Log the error for debugging
        print(f"PDF Export Error: {str(e)}")
        flash(f'حدث خطأ أثناء تصدير ملف PDF: {str(e)}', 'danger')
        return redirect(url_for('patients.index'))

# Add import from Excel functionality
@patients_bp.route('/import/excel', methods=['GET', 'POST'])
@login_required
def import_excel():
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('لم يتم تحديد ملف', 'danger')
            return redirect(request.url)
        
        file = request.files['file']
        
        if file.filename == '':
            flash('لم يتم تحديد ملف', 'danger')
            return redirect(request.url)
        
        if file and file.filename.endswith(('.xlsx', '.xls')):
            try:
                # Read Excel file
                df = pd.read_excel(file)
                
                # Process each row
                success_count = 0
                error_count = 0
                
                for _, row in df.iterrows():
                    try:
                        # Check if patient with same name and birth date already exists
                        birth_date = pd.to_datetime(row['تاريخ الميلاد']).date() if pd.notna(row['تاريخ الميلاد']) else None
                        
                        existing_patient = Patient.query.filter_by(name=row['الاسم']).first()
                        if existing_patient and existing_patient.birth_date == birth_date:
                            # Update existing patient
                            existing_patient.gender = row['الجنس'] if pd.notna(row['الجنس']) else existing_patient.gender
                            existing_patient.address = row['العنوان'] if pd.notna(row['العنوان']) else existing_patient.address
                            existing_patient.phone = row['رقم الهاتف'] if pd.notna(row['رقم الهاتف']) else existing_patient.phone
                            existing_patient.email = row['البريد الإلكتروني'] if pd.notna(row['البريد الإلكتروني']) else existing_patient.email
                            existing_patient.blood_type = row['فصيلة الدم'] if pd.notna(row['فصيلة الدم']) else existing_patient.blood_type
                            existing_patient.allergies = row['الحساسية'] if pd.notna(row['الحساسية']) else existing_patient.allergies
                            existing_patient.medical_history = row['التاريخ الطبي'] if pd.notna(row['التاريخ الطبي']) else existing_patient.medical_history
                        else:
                            # Create new patient
                            new_patient = Patient(
                                name=row['الاسم'],
                                birth_date=birth_date,
                                gender=row['الجنس'] if pd.notna(row['الجنس']) else 'غير محدد',
                                address=row['العنوان'] if pd.notna(row['العنوان']) else None,
                                phone=row['رقم الهاتف'] if pd.notna(row['رقم الهاتف']) else None,
                                email=row['البريد الإلكتروني'] if pd.notna(row['البريد الإلكتروني']) else None,
                                blood_type=row['فصيلة الدم'] if pd.notna(row['فصيلة الدم']) else None,
                                allergies=row['الحساسية'] if pd.notna(row['الحساسية']) else None,
                                medical_history=row['التاريخ الطبي'] if pd.notna(row['التاريخ الطبي']) else None,
                                created_at=datetime.now()
                            )
                            db.session.add(new_patient)
                        
                        success_count += 1
                    except Exception as e:
                        error_count += 1
                        print(f"Error importing row: {e}")
                
                db.session.commit()
                flash(f'تم استيراد {success_count} مريض بنجاح. {error_count} سجل به أخطاء.', 'success')
                return redirect(url_for('patients.index'))
            
            except Exception as e:
                flash(f'حدث خطأ أثناء استيراد الملف: {str(e)}', 'danger')
                return redirect(request.url)
        else:
            flash('يرجى تحميل ملف Excel (.xlsx, .xls)', 'danger')
            return redirect(request.url)
    
    return render_template('patients/import.html')

@patients_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if request.method == 'POST':
        name = request.form.get('name')
        birth_date = datetime.strptime(request.form.get('birth_date'), '%Y-%m-%d')
        gender = request.form.get('gender')
        address = request.form.get('address')
        phone = request.form.get('phone')
        email = request.form.get('email')
        medical_history = request.form.get('medical_history')
        blood_type = request.form.get('blood_type')
        allergies = request.form.get('allergies')
        
        new_patient = Patient(
            name=name,
            birth_date=birth_date,
            gender=gender,
            address=address,
            phone=phone,
            email=email,
            medical_history=medical_history,
            blood_type=blood_type,
            allergies=allergies
        )
        
        db.session.add(new_patient)
        db.session.commit()
        
        flash('تمت إضافة المريض بنجاح!', 'success')
        return redirect(url_for('patients.index'))
    
    return render_template('patients/add.html')

# Add this after the add route

@patients_bp.route('/view/<int:patient_id>')
@login_required
def view(patient_id):
    """عرض تفاصيل المريض"""
    patient = Patient.query.get_or_404(patient_id)
    return render_template('patients/view.html', patient=patient)

# Add this after the view route

@patients_bp.route('/edit/<int:patient_id>', methods=['GET', 'POST'])
@login_required
def edit(patient_id):
    """تعديل بيانات المريض"""
    patient = Patient.query.get_or_404(patient_id)
    
    if request.method == 'POST':
        try:
            # Update patient data
            patient.name = request.form.get('name')
            birth_date_str = request.form.get('birth_date')
            patient.birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d') if birth_date_str else patient.birth_date
            patient.gender = request.form.get('gender')
            patient.blood_type = request.form.get('blood_type')
            patient.phone = request.form.get('phone')
            patient.email = request.form.get('email')
            patient.address = request.form.get('address')
            patient.medical_history = request.form.get('medical_history')
            patient.allergies = request.form.get('allergies')
            
            db.session.commit()
            flash('تم تحديث بيانات المريض بنجاح', 'success')
            return redirect(url_for('patients.view', patient_id=patient.id))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث بيانات المريض: {str(e)}', 'danger')
    
    return render_template('patients/edit.html', patient=patient)

# Add this after the edit route

# Add this route to handle patient deletion
@patients_bp.route('/delete/<int:patient_id>', methods=['POST'])
@login_required
def delete(patient_id):
    try:
        # Get the patient by ID
        patient = Patient.query.get_or_404(patient_id)
        
        # Delete the patient
        db.session.delete(patient)
        db.session.commit()
        
        flash('تم حذف المريض بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المريض: {str(e)}', 'danger')
    
    return redirect(url_for('patients.index'))