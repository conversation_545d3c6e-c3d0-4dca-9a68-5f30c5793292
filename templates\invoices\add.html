{% extends "base.html" %}

{% block page_title %}إضافة فاتورة جديدة{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">بيانات الفاتورة الجديدة</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('invoices.add') }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="patient_id" class="form-label">المريض <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="patientSearch" placeholder="ابحث بالاسم أو رقم الهاتف أو البريد الإلكتروني...">
                        <button class="btn btn-outline-secondary" type="button" id="clearPatientSearch">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div id="patientSearchResults" class="position-absolute bg-white shadow rounded p-0 d-none" 
                         style="z-index: 1000; width: calc(50% - 2rem); max-height: 250px; overflow-y: auto; border: 1px solid #dee2e6;"></div>
                    <div id="searchLoader" class="position-absolute d-none" style="top: 10px; right: 50px;">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">جاري البحث...</span>
                        </div>
                    </div>
                    <select class="form-select mt-2" id="patient_id" name="patient_id" required>
                        <option value="" selected disabled>اختر المريض</option>
                        {% for patient in patients %}
                        <option value="{{ patient.id }}">{{ patient.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="doctor_id" class="form-label">الطبيب <span class="text-danger">*</span></label>
                    <select class="form-select" id="doctor_id" name="doctor_id" required>
                        <option value="" selected disabled>اختر الطبيب</option>
                        {% for doctor in doctors %}
                        <option value="{{ doctor.id }}">{{ doctor.name }} - {{ doctor.specialization }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="appointment_id" class="form-label">الموعد (اختياري)</label>
                    <select class="form-select" id="appointment_id" name="appointment_id">
                        <option value="">بدون موعد</option>
                        {% for appointment in appointments %}
                        <option value="{{ appointment.id }}">{{ appointment.appointment_datetime.strftime('%Y-%m-%d %H:%M') }} - {{ appointment.patient.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <!-- Treatment field removed -->
                <div class="col-md-6 mb-3">
                    <label for="invoice_date" class="form-label">تاريخ الفاتورة <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="invoice_date" name="invoice_date" value="{{ today }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                    <input type="date" class="form-control" id="due_date" name="due_date" value="{{ due_date }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="number" step="0.01" class="form-control" id="amount" name="amount" required>
                        <span class="input-group-text">د.ل</span>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="tax" class="form-label">الضريبة</label>
                    <div class="input-group">
                        <input type="number" step="0.01" class="form-control" id="tax" name="tax" value="0">
                        <span class="input-group-text">د.ل</span>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="discount" class="form-label">الخصم</label>
                    <div class="input-group">
                        <input type="number" step="0.01" class="form-control" id="discount" name="discount" value="0">
                        <span class="input-group-text">د.ل</span>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="total_display" class="form-label">المبلغ الإجمالي</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="total_display" readonly>
                        <span class="input-group-text">د.ل</span>
                    </div>
                    <!-- Hidden field to store the total amount -->
                    <input type="hidden" id="total_amount" name="total_amount" value="0">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="status" class="form-label">حالة الفاتورة <span class="text-danger">*</span></label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="غير مدفوعة" selected>غير مدفوعة</option>
                        <option value="مدفوعة">مدفوعة</option>
                        <option value="مدفوعة جزئياً">مدفوعة جزئياً</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="payment_method" class="form-label">طريقة الدفع</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="">اختر طريقة الدفع</option>
                        <option value="نقداً">نقداً</option>
                        <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                        <option value="تحويل بنكي">تحويل بنكي</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('invoices.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ الفاتورة
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    // حساب المبلغ الإجمالي تلقائياً
    document.addEventListener('DOMContentLoaded', function() {
        const amountInput = document.getElementById('amount');
        const taxInput = document.getElementById('tax');
        const discountInput = document.getElementById('discount');
        const totalDisplay = document.getElementById('total_display');
        const totalAmountField = document.getElementById('total_amount');
        
        function updateTotal() {
            const amount = parseFloat(amountInput.value) || 0;
            const tax = parseFloat(taxInput.value) || 0;
            const discount = parseFloat(discountInput.value) || 0;
            
            const total = amount + tax - discount;
            
            // Update the display and hidden field
            totalDisplay.value = total.toFixed(2);
            totalAmountField.value = total.toFixed(2);
        }
        
        // Call updateTotal initially to set default values
        updateTotal();
        
        // Add event listeners
        amountInput.addEventListener('input', updateTotal);
        taxInput.addEventListener('input', updateTotal);
        discountInput.addEventListener('input', updateTotal);
        
        // Patient search functionality - Enhanced version
        const patientSearchInput = document.getElementById('patientSearch');
        const patientSearchResults = document.getElementById('patientSearchResults');
        const patientSelect = document.getElementById('patient_id');
        const clearPatientSearchBtn = document.getElementById('clearPatientSearch');
        const searchLoader = document.getElementById('searchLoader');
        
        // Patient data for search
        const patients = [
            {% for patient in patients %}
                {
                    id: {{ patient.id }},
                    name: "{{ patient.name }}",
                    phone: "{{ patient.phone or '' }}",
                    email: "{{ patient.email or '' }}",
                    file_number: "{{ patient.file_number or '' }}",
                    national_id: "{{ patient.national_id or '' }}"
                },
            {% endfor %}
        ];
        
        // Debounce function to improve search performance
        function debounce(func, wait) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), wait);
            };
        }
        
        // Search function
        const performSearch = debounce(function(searchTerm) {
            if (searchTerm.length < 2) {
                patientSearchResults.classList.add('d-none');
                searchLoader.classList.add('d-none');
                return;
            }
            
            searchLoader.classList.remove('d-none');
            
            // Simulate network delay for better UX
            setTimeout(() => {
                const matches = patients.filter(patient => 
                    patient.name.toLowerCase().includes(searchTerm) || 
                    (patient.phone && patient.phone.includes(searchTerm)) ||
                    (patient.email && patient.email.toLowerCase().includes(searchTerm)) ||
                    (patient.file_number && patient.file_number.includes(searchTerm)) ||
                    (patient.national_id && patient.national_id.includes(searchTerm))
                );
                
                if (matches.length > 0) {
                    patientSearchResults.innerHTML = `
                        <div class="search-header bg-light p-2 border-bottom">
                            <small class="text-muted">تم العثور على ${matches.length} نتيجة</small>
                        </div>
                        ${matches.map(patient => 
                            `<div class="search-item p-2 border-bottom hover-bg-light" data-id="${patient.id}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="fw-bold">${patient.name}</div>
                                    ${patient.file_number ? `<span class="badge bg-info">ملف: ${patient.file_number}</span>` : ''}
                                </div>
                                <div class="small text-muted">
                                    ${patient.phone ? `<i class="fas fa-phone me-1"></i>${patient.phone}` : ''}
                                    ${patient.email ? `<i class="fas fa-envelope ms-2 me-1"></i>${patient.email}` : ''}
                                    ${patient.national_id ? `<i class="fas fa-id-card ms-2 me-1"></i>${patient.national_id}` : ''}
                                </div>
                            </div>`
                        ).join('')}
                    `;
                    
                    patientSearchResults.classList.remove('d-none');
                    
                    // Add click event to search results
                    document.querySelectorAll('.search-item').forEach(item => {
                        item.addEventListener('click', function() {
                            const patientId = this.dataset.id;
                            
                            // Set the select value
                            patientSelect.value = patientId;
                            
                            // Update the search input with the selected patient name
                            patientSearchInput.value = this.querySelector('.fw-bold').textContent;
                            
                            // Trigger change event on select
                            const event = new Event('change');
                            patientSelect.dispatchEvent(event);
                            
                            // Hide the results
                            patientSearchResults.classList.add('d-none');
                            
                            // Add visual feedback for selection
                            patientSearchInput.classList.add('is-valid');
                            setTimeout(() => {
                                patientSearchInput.classList.remove('is-valid');
                            }, 2000);
                        });
                        
                        // Add hover effect
                        item.addEventListener('mouseenter', function() {
                            this.classList.add('bg-light');
                        });
                        
                        item.addEventListener('mouseleave', function() {
                            this.classList.remove('bg-light');
                        });
                    });
                } else {
                    patientSearchResults.innerHTML = `
                        <div class="p-3 text-center">
                            <i class="fas fa-search-minus mb-2 text-muted" style="font-size: 1.5rem;"></i>
                            <div>لا توجد نتائج مطابقة</div>
                            <small class="text-muted">حاول استخدام كلمات بحث مختلفة</small>
                        </div>
                    `;
                    patientSearchResults.classList.remove('d-none');
                }
                
                searchLoader.classList.add('d-none');
            }, 300); // Simulate search delay
        }, 300); // Debounce delay
        
        // Search input event
        patientSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim().toLowerCase();
            performSearch(searchTerm);
        });
        
        // Focus event to show results again if there's text
        patientSearchInput.addEventListener('focus', function() {
            if (this.value.trim().length >= 2) {
                performSearch(this.value.trim().toLowerCase());
            }
        });
        
        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!patientSearchInput.contains(e.target) && !patientSearchResults.contains(e.target)) {
                patientSearchResults.classList.add('d-none');
            }
        });
        
        // Clear search button
        clearPatientSearchBtn.addEventListener('click', function() {
            patientSearchInput.value = '';
            patientSelect.selectedIndex = 0;
            patientSearchResults.classList.add('d-none');
            patientSearchInput.focus();
        });
        
        // Update search input when select changes
        patientSelect.addEventListener('change', function() {
            const selectedPatient = patients.find(p => p.id == this.value);
            if (selectedPatient) {
                patientSearchInput.value = selectedPatient.name;
            } else {
                patientSearchInput.value = '';
            }
        });
        
        // Add keyboard navigation for search results
        patientSearchInput.addEventListener('keydown', function(e) {
            if (patientSearchResults.classList.contains('d-none')) return;
            
            const items = patientSearchResults.querySelectorAll('.search-item');
            if (!items.length) return;
            
            let activeItem = patientSearchResults.querySelector('.search-item.bg-light');
            let activeIndex = -1;
            
            if (activeItem) {
                activeIndex = Array.from(items).indexOf(activeItem);
            }
            
            // Down arrow
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                if (activeItem) activeItem.classList.remove('bg-light');
                activeIndex = (activeIndex + 1) % items.length;
                items[activeIndex].classList.add('bg-light');
                items[activeIndex].scrollIntoView({ block: 'nearest' });
            }
            
            // Up arrow
            else if (e.key === 'ArrowUp') {
                e.preventDefault();
                if (activeItem) activeItem.classList.remove('bg-light');
                activeIndex = (activeIndex - 1 + items.length) % items.length;
                items[activeIndex].classList.add('bg-light');
                items[activeIndex].scrollIntoView({ block: 'nearest' });
            }
            
            // Enter key
            else if (e.key === 'Enter' && activeItem) {
                e.preventDefault();
                activeItem.click();
            }
            
            // Escape key
            else if (e.key === 'Escape') {
                patientSearchResults.classList.add('d-none');
            }
        });
        
        // Auto-select doctor when appointment is selected
        const appointmentSelect = document.getElementById('appointment_id');
        const doctorSelect = document.getElementById('doctor_id');
        
        appointmentSelect.addEventListener('change', function() {
            const appointmentId = this.value;
            if (appointmentId) {
                // Find the doctor and patient associated with this appointment
                {% for appointment in appointments %}
                if ('{{ appointment.id }}' === appointmentId) {
                    // Set the doctor select value
                    Array.from(doctorSelect.options).forEach(option => {
                        if (option.value === '{{ appointment.doctor_id }}') {
                            option.selected = true;
                        }
                    });
                    
                    // Set the patient select value
                    Array.from(patientSelect.options).forEach(option => {
                        if (option.value === '{{ appointment.patient_id }}') {
                            option.selected = true;
                        }
                    });
                }
                {% endfor %}
            }
        });
        
        // Treatment related code removed
    });
</script>
{% endblock %}