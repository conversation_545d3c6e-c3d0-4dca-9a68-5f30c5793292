{% extends "base.html" %}

{% block title %}تعديل موعد{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تعديل موعد</h5>
        <a href="{{ url_for('appointments.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة
        </a>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('appointments.edit', appointment_id=appointment.id) }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="patient_id" class="form-label">المريض</label>
                    <select class="form-select" id="patient_id" name="patient_id" required>
                        {% for patient in patients %}
                        <option value="{{ patient.id }}" {% if patient.id == appointment.patient_id %}selected{% endif %}>
                            {{ patient.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="doctor_id" class="form-label">الطبيب</label>
                    <select class="form-select" id="doctor_id" name="doctor_id" required>
                        {% for doctor in doctors %}
                        <option value="{{ doctor.id }}" {% if doctor.id == appointment.doctor_id %}selected{% endif %}>
                            {{ doctor.name }} - {{ doctor.specialization }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="appointment_date" class="form-label">تاريخ الموعد</label>
                    <input type="date" class="form-control" id="appointment_date" name="appointment_date" value="{{ appointment_date }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="appointment_time" class="form-label">وقت الموعد</label>
                    <input type="time" class="form-control" id="appointment_time" name="appointment_time" value="{{ appointment_time }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="مؤكد" {% if appointment.status == 'مؤكد' %}selected{% endif %}>مؤكد</option>
                        <option value="ملغي" {% if appointment.status == 'ملغي' %}selected{% endif %}>ملغي</option>
                        <option value="منجز" {% if appointment.status == 'منجز' %}selected{% endif %}>منجز</option>
                    </select>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ appointment.notes }}</textarea>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('appointments.index') }}" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}