{% extends "base.html" %}

{% block page_title %}إعدادات النظام{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إعدادات النظام</h5>
            </div>
            <div class="card-body">
                <!-- Make sure the form action points to the correct URL -->
                <!-- Change the form action from url_for('settings') to url_for('update_settings') -->
                <form method="POST" action="{{ url_for('update_settings') }}">
                    <div class="mb-4">
                        <h6 class="fw-bold">معلومات العيادة</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="clinic_name" class="form-label">اسم العيادة</label>
                                <input type="text" class="form-control" id="clinic_name" name="clinic_name" value="{{ settings.clinic_name }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="clinic_phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="clinic_phone" name="clinic_phone" value="{{ settings.clinic_phone }}">
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="clinic_address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="clinic_address" name="clinic_address" rows="2">{{ settings.clinic_address }}</textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="fw-bold">إعدادات المواعيد</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="appointment_interval" class="form-label">مدة الموعد (بالدقائق)</label>
                                <input type="number" class="form-control" id="appointment_interval" name="appointment_interval" value="{{ settings.appointment_interval }}" min="10" step="5">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="working_hours_start" class="form-label">بداية ساعات العمل</label>
                                <input type="time" class="form-control" id="working_hours_start" name="working_hours_start" value="{{ settings.working_hours_start }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="working_hours_end" class="form-label">نهاية ساعات العمل</label>
                                <input type="time" class="form-control" id="working_hours_end" name="working_hours_end" value="{{ settings.working_hours_end }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> حفظ الإعدادات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}