from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_user, logout_user, login_required, current_user
from models import User
from datetime import datetime

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = True if request.form.get('remember') else False
        
        user = User.query.filter_by(username=username).first()
        
        if not user or not user.check_password(password):
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
            return redirect(url_for('auth.login'))
        
        login_user(user, remember=remember)
        flash(f'مرحبًا بك {user.username}!', 'success')
        
        next_page = request.args.get('next')
        return redirect(next_page or url_for('main.dashboard'))
    
    current_year = datetime.now().year
    return render_template('login.html', current_year=current_year)

# Find the logout route in this file and modify it
# The current implementation likely has a flash message when logging out

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    # Remove the flash message that says "تم تسجيل الخروج بنجاح"
    # or any similar logout success message
    return redirect(url_for('login'))

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        if not current_user.check_password(current_password):
            flash('كلمة المرور الحالية غير صحيحة', 'danger')
            return redirect(url_for('auth.profile'))
        
        if new_password:
            if new_password != confirm_password:
                flash('كلمات المرور الجديدة غير متطابقة', 'danger')
                return redirect(url_for('auth.profile'))
            
            current_user.set_password(new_password)
            db.session.commit()
            flash('تم تحديث كلمة المرور بنجاح', 'success')
            return redirect(url_for('auth.profile'))
        
        flash('لم يتم إجراء أي تغييرات', 'info')
        return redirect(url_for('auth.profile'))
    
    return render_template('profile.html')