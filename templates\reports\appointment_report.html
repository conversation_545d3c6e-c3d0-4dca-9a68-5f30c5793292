{% extends "base.html" %}

{% block page_title %}تقرير المواعيد{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير المواعيد</h5>
        <div>
            <a href="{{ url_for('reports') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <div class="row">
                <div class="col-md-3">
                    <p><strong>الطبيب:</strong> {{ doctor_name if doctor_name else 'الكل' }}</p>
                </div>
                <div class="col-md-3">
                    <p><strong>الحالة:</strong> {{ status if status else 'الكل' }}</p>
                </div>
                <div class="col-md-3">
                    <p><strong>من تاريخ:</strong> {{ date_from if date_from else 'غير محدد' }}</p>
                </div>
                <div class="col-md-3">
                    <p><strong>إلى تاريخ:</strong> {{ date_to if date_to else 'غير محدد' }}</p>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المريض</th>
                        <th>الطبيب</th>
                        <th>التاريخ</th>
                        <th>الوقت</th>
                        <th>الحالة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if appointments %}
                        {% for appointment in appointments %}
                        <tr>
                            <td>{{ appointment.id }}</td>
                            <td>{{ appointment.patient.name }}</td>
                            <td>{{ appointment.doctor.name }}</td>
                            <td>{{ appointment.appointment_datetime.strftime('%Y-%m-%d') }}</td>
                            <td>{{ appointment.appointment_datetime.strftime('%H:%M') }}</td>
                            <td>{{ appointment.status }}</td>
                            <td>{{ appointment.notes }}</td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="text-center">لا توجد مواعيد في هذه الفترة</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <div class="mt-3">
            <p><strong>إجمالي عدد المواعيد:</strong> {{ appointments|length }}</p>
        </div>
    </div>
</div>
{% endblock %}