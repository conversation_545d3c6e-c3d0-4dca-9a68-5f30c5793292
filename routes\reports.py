from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models import db, Patient, Appointment, Doctor, Treatment, Invoice, User, LoginLog
from datetime import datetime, timedelta
from sqlalchemy import func, extract
import calendar
from utils import ensure_json_serializable  # Update this import

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية للتقارير والإحصائيات"""
    return render_template('reports/index.html')

@reports_bp.route('/patients')
@login_required
def patients_report():
    # Get query parameters
    sort_by = request.args.get('sort', 'name')
    order = request.args.get('order', 'asc')
    
    # Build the query
    query = Patient.query
    
    # Apply sorting
    if order == 'asc':
        query = query.order_by(getattr(Patient, sort_by))
    else:
        query = query.order_by(db.desc(getattr(Patient, sort_by)))
    
    # Execute the query
    patients = query.all()
    
    # Get statistics
    total_patients = len(patients)
    
    # Calculate new patients this month
    current_date = datetime.now()
    first_day_of_month = datetime(current_date.year, current_date.month, 1)
    new_patients_count = Patient.query.filter(Patient.created_at >= first_day_of_month).count()
    
    # Calculate average age
    current_year = datetime.now().year
    ages = [current_year - patient.birth_date.year for patient in patients if patient.birth_date]
    avg_age = sum(ages) / len(ages) if ages else 0
    
    # Gender distribution
    male_count = Patient.query.filter_by(gender='ذكر').count()
    female_count = Patient.query.filter_by(gender='أنثى').count()
    
    # Most frequent visitors (patients with most appointments)
    most_frequent = db.session.query(
        Patient, func.count(Appointment.id).label('visit_count')
    ).join(Appointment).group_by(Patient.id).order_by(db.desc('visit_count')).limit(5).all()
    
    # Get appointment counts for each patient
    patient_stats = []
    for patient in patients:
        appointments_count = Appointment.query.filter_by(patient_id=patient.id).count()
        treatments_count = Treatment.query.filter_by(patient_id=patient.id).count()
        invoices_count = Invoice.query.filter_by(patient_id=patient.id).count()
        
        patient_stats.append({
            'patient': patient,
            'appointments_count': appointments_count,
            'treatments_count': treatments_count,
            'invoices_count': invoices_count
        })
    
    return render_template('reports/patients.html', 
                          patient_stats=patient_stats,
                          total_patients=total_patients,
                          new_patients_count=new_patients_count,
                          avg_age=round(avg_age, 1),
                          male_count=male_count,
                          female_count=female_count,
                          most_frequent=most_frequent,
                          sort_by=sort_by,
                          order=order)

@reports_bp.route('/appointments')
@login_required
def appointments_report():
    """تقرير المواعيد"""
    # إحصائيات عامة
    total_appointments = Appointment.query.count()
    completed_appointments = Appointment.query.filter_by(status='منجز').count()
    cancelled_appointments = Appointment.query.filter_by(status='ملغي').count()
    
    # توزيع المواعيد حسب الأطباء
    doctor_distribution = db.session.query(
        Doctor.name, func.count(Appointment.id)
    ).join(Doctor).group_by(Doctor.id).all()
    
    # توزيع المواعيد حسب الأيام
    day_distribution = db.session.query(
        func.strftime('%w', Appointment.appointment_datetime).label('day_of_week'),
        func.count(Appointment.id)
    ).group_by('day_of_week').all()
    
    # تحويل أرقام الأيام إلى أسماء
    days_of_week = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
    day_distribution = [(days_of_week[int(day)], count) for day, count in day_distribution]
    
    return render_template('reports/appointments.html',
                          total_appointments=total_appointments,
                          completed_appointments=completed_appointments,
                          cancelled_appointments=cancelled_appointments,
                          doctor_distribution=doctor_distribution,
                          day_distribution=day_distribution)

@reports_bp.route('/financial')
@login_required
def financial_report():
    """التقرير المالي"""
    # إجمالي الإيرادات
    total_revenue = db.session.query(func.sum(Invoice.total_amount)).scalar() or 0
    
    # الإيرادات حسب الحالة
    paid_revenue = db.session.query(func.sum(Invoice.total_amount)).filter(
        Invoice.status == 'مدفوعة'
    ).scalar() or 0
    
    unpaid_revenue = db.session.query(func.sum(Invoice.total_amount)).filter(
        Invoice.status == 'غير مدفوعة'
    ).scalar() or 0
    
    partially_paid_revenue = db.session.query(func.sum(Invoice.total_amount)).filter(
        Invoice.status == 'مدفوعة جزئياً'
    ).scalar() or 0
    
    # الإيرادات الشهرية للسنة الحالية
    current_year = datetime.now().year
    monthly_revenue = []
    
    for month in range(1, 13):
        month_revenue = db.session.query(func.sum(Invoice.total_amount)).filter(
            extract('month', Invoice.invoice_date) == month,
            extract('year', Invoice.invoice_date) == current_year
        ).scalar() or 0
        
        monthly_revenue.append({
            'month': calendar.month_name[month],
            'revenue': month_revenue
        })
    
    return render_template('reports/financial.html',
                          total_revenue=total_revenue,
                          paid_revenue=paid_revenue,
                          unpaid_revenue=unpaid_revenue,
                          partially_paid_revenue=partially_paid_revenue,
                          monthly_revenue=monthly_revenue)

@reports_bp.route('/treatments')
@login_required
def treatments_report():
    """تقرير العلاجات"""
    # إجمالي العلاجات
    total_treatments = Treatment.query.count()
    
    # العلاجات الأكثر وصفاً
    common_medications = db.session.query(
        Treatment.medication_name, func.count(Treatment.id).label('count')
    ).group_by(Treatment.medication_name).order_by(db.desc('count')).limit(10).all()
    
    # توزيع العلاجات حسب الأطباء
    doctor_distribution = db.session.query(
        Doctor.name, func.count(Treatment.id)
    ).join(Doctor).group_by(Doctor.id).all()
    
    return render_template('reports/treatments.html',
                          total_treatments=total_treatments,
                          common_medications=common_medications,
                          doctor_distribution=doctor_distribution)

@reports_bp.route('/custom', methods=['GET', 'POST'])
@login_required
def custom_report():
    """تقرير مخصص"""
    if request.method == 'POST':
        report_type = request.form.get('report_type')
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')
        
        # تحويل التواريخ
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else None
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d') if end_date_str else None
        
        if end_date:
            # تعديل نهاية اليوم لتشمل كل اليوم
            end_date = datetime.combine(end_date.date(), datetime.max.time())
        
        # بناء الاستعلام حسب نوع التقرير
        if report_type == 'patients':
            query = Patient.query
            if start_date:
                query = query.filter(Patient.created_at >= start_date)
            if end_date:
                query = query.filter(Patient.created_at <= end_date)
            
            results = query.all()
            return render_template('reports/custom_patients.html', 
                                  results=results, 
                                  start_date=start_date_str, 
                                  end_date=end_date_str)
        
        elif report_type == 'appointments':
            query = Appointment.query
            if start_date:
                query = query.filter(Appointment.appointment_datetime >= start_date)
            if end_date:
                query = query.filter(Appointment.appointment_datetime <= end_date)
            
            results = query.all()
            return render_template('reports/custom_appointments.html', 
                                  results=results, 
                                  start_date=start_date_str, 
                                  end_date=end_date_str)
        
        elif report_type == 'invoices':
            query = Invoice.query
            if start_date:
                query = query.filter(Invoice.invoice_date >= start_date)
            if end_date:
                query = query.filter(Invoice.invoice_date <= end_date)
            
            results = query.all()
            return render_template('reports/custom_invoices.html', 
                                  results=results, 
                                  start_date=start_date_str, 
                                  end_date=end_date_str)
    
    return render_template('reports/custom.html')

@reports_bp.route('/doctors_revenue')
@login_required
def doctors_revenue():
    """عرض تقرير إيرادات الأطباء"""
    try:
        # Get all doctors
        doctors = Doctor.query.all()
        
        # Get all invoices with paid status
        invoices = Invoice.query.filter(Invoice.status == 'مدفوعة').all()
        
        # Calculate revenue for each doctor
        doctors_data = []
        
        for doctor in doctors:
            # Get invoices directly related to this doctor
            doctor_invoices = [inv for inv in invoices if inv.doctor_id == doctor.id]
            
            # Calculate total revenue
            total_revenue = sum(inv.total_amount for inv in doctor_invoices)
            
            # Count number of paid invoices
            paid_invoices = len(doctor_invoices)
            
            doctors_data.append({
                'doctor': doctor,
                'total_revenue': total_revenue,
                'paid_appointments': paid_invoices
            })
        
        # Sort doctors by revenue (highest first)
        doctors_data.sort(key=lambda x: x['total_revenue'], reverse=True)
        
        # Calculate total clinic revenue
        total_clinic_revenue = sum(doc['total_revenue'] for doc in doctors_data)
        
        return render_template('reports/doctors_revenue.html', 
                              doctors_data=doctors_data,
                              total_clinic_revenue=total_clinic_revenue)
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        print(f"Error generating doctor revenue report: {e}")
        return redirect(url_for('reports.index'))

@reports_bp.route('/doctors_monthly_dues', methods=['GET', 'POST'])
@login_required
def doctors_monthly_dues():
    """عرض تقرير مستحقات الأطباء الشهرية"""
    try:
        # Get all doctors
        doctors = Doctor.query.all()
        
        # Get available years from invoices
        years = db.session.query(db.extract('year', Invoice.invoice_date)).distinct().order_by(db.extract('year', Invoice.invoice_date).desc()).all()
        years = [year[0] for year in years]
        
        # If no years found, add current year
        if not years:
            years = [datetime.now().year]
        
        # Default to current month and year
        current_date = datetime.now()
        selected_month = int(request.form.get('month', current_date.month))
        selected_year = int(request.form.get('year', current_date.year))
        selected_doctor_id = request.form.get('doctor_id', '')
        
        doctor_dues = []
        total_revenue = 0
        total_doctor_dues = 0
        total_clinic_dues = 0
        
        if request.method == 'POST':
            # Filter by month and year
            start_date = datetime(selected_year, selected_month, 1)
            if selected_month == 12:
                end_date = datetime(selected_year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = datetime(selected_year, selected_month + 1, 1) - timedelta(days=1)
            
            # Get paid invoices for the selected period
            query = Invoice.query.filter(
                Invoice.status == 'مدفوعة',
                Invoice.invoice_date >= start_date,
                Invoice.invoice_date <= end_date
            )
            
            # Filter by doctor if selected
            if selected_doctor_id:
                query = query.filter(Invoice.doctor_id == selected_doctor_id)
                doctors_to_process = [Doctor.query.get(selected_doctor_id)]
            else:
                doctors_to_process = doctors
            
            invoices = query.all()
            
            # Calculate dues for each doctor
            for doctor in doctors_to_process:
                # Get invoices for this doctor
                doctor_invoices = [inv for inv in invoices if inv.doctor_id == doctor.id]
                
                # Calculate total revenue for this doctor
                doctor_revenue = sum(inv.total_amount for inv in doctor_invoices)
                
                # Skip if no revenue
                if doctor_revenue == 0:
                    continue
                
                # Default percentage is 50% if not specified
                doctor_percentage = getattr(doctor, 'percentage', 50)
                
                # Calculate doctor's due
                doctor_due = doctor_revenue * (doctor_percentage / 100)
                clinic_due = doctor_revenue - doctor_due
                
                doctor_dues.append({
                    'doctor': doctor,
                    'total_revenue': doctor_revenue,
                    'percentage': doctor_percentage,
                    'doctor_due': doctor_due,
                    'clinic_due': clinic_due
                })
                
                total_revenue += doctor_revenue
                total_doctor_dues += doctor_due
                total_clinic_dues += clinic_due
            
            # Sort by revenue (highest first)
            doctor_dues.sort(key=lambda x: x['total_revenue'], reverse=True)
        
        return render_template('reports/doctors_monthly_dues.html',
                              doctors=doctors,
                              years=years,
                              selected_month=selected_month,
                              selected_year=selected_year,
                              selected_doctor_id=selected_doctor_id,
                              doctor_dues=doctor_dues,
                              total_revenue=total_revenue,
                              total_doctor_dues=total_doctor_dues,
                              total_clinic_dues=total_clinic_dues)
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        print(f"Error generating doctor monthly dues report: {e}")
        return redirect(url_for('reports.index'))

# Add this route for the general login history
@reports_bp.route('/login_history')
@login_required
def login_history():
    """عرض تقرير سجل تسجيل الدخول والخروج"""
    try:
        # التحقق من صلاحيات المستخدم (فقط المدير يمكنه الوصول)
        if current_user.role != 'مدير':
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('dashboard'))
        
        # الحصول على معلمات التصفية
        user_id = request.args.get('user_id', type=int)
        action = request.args.get('action')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        page = request.args.get('page', 1, type=int)
        per_page = 50  # عدد السجلات في كل صفحة
        
        # بناء الاستعلام الأساسي
        base_query = LoginLog.query.join(User, LoginLog.user_id == User.id)
        
        # تطبيق الفلاتر
        if user_id:
            base_query = base_query.filter(LoginLog.user_id == user_id)
        
        if action:
            base_query = base_query.filter(LoginLog.action == action)
        
        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                base_query = base_query.filter(LoginLog.timestamp >= start_date)
            except ValueError:
                pass
        
        if end_date_str:
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d') + timedelta(days=1)
                base_query = base_query.filter(LoginLog.timestamp < end_date)
            except ValueError:
                pass
        
        # ترتيب النتائج حسب التاريخ (الأحدث أولاً)
        base_query = base_query.order_by(LoginLog.timestamp.desc())
        
        # تقسيم النتائج إلى صفحات
        pagination = base_query.paginate(page=page, per_page=per_page, error_out=False)
        logs = pagination.items
        
        # الحصول على قائمة المستخدمين للفلتر
        users = User.query.all()
        
        # حساب الإحصائيات
        total_logins = LoginLog.query.filter_by(action='login').count()
        total_logouts = LoginLog.query.filter_by(action='logout').count()
        total_users = User.query.count()
        
        # حساب متوسط مدة الجلسة
        sessions = []
        login_logs = {}
        
        for log in LoginLog.query.filter_by(action='login').all():
            if log.user_id not in login_logs:
                login_logs[log.user_id] = []
            login_logs[log.user_id].append(log)
        
        for log in LoginLog.query.filter_by(action='logout').all():
            if log.user_id in login_logs:
                for login_log in login_logs[log.user_id]:
                    if login_log.timestamp < log.timestamp:
                        duration = (log.timestamp - login_log.timestamp).total_seconds() // 60
                        sessions.append(duration)
                        login_logs[log.user_id].remove(login_log)
                        break
        
        avg_session_time = f"{int(sum(sessions) / len(sessions)) if sessions else 0} دقيقة"
        
        # إحصائيات المستخدمين
        user_stats = []
        for user in users:
            user_logins = LoginLog.query.filter_by(user_id=user.id, action='login').all()
            
            if user_logins:
                last_login = max(user_logins, key=lambda x: x.timestamp)
                last_login_time = last_login.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                
                # حساب متوسط مدة الجلسة للمستخدم
                user_sessions = []
                user_login_logs = {}
                
                for log in LoginLog.query.filter_by(user_id=user.id, action='login').all():
                    if log.id not in user_login_logs:
                        user_login_logs[log.id] = log
                
                for log in LoginLog.query.filter_by(user_id=user.id, action='logout').all():
                    for login_id, login_log in list(user_login_logs.items()):
                        if login_log.timestamp < log.timestamp:
                            duration = (log.timestamp - login_log.timestamp).total_seconds() // 60
                            user_sessions.append(duration)
                            del user_login_logs[login_id]
                            break
                
                avg_user_session = f"{int(sum(user_sessions) / len(user_sessions)) if user_sessions else 0} دقيقة"
                
                # تحديد ساعة الذروة
                login_hours = [log.timestamp.hour for log in user_logins]
                peak_hour = max(set(login_hours), key=login_hours.count) if login_hours else 0
                peak_hour_str = f"{peak_hour}:00 - {peak_hour+1}:00"
                
                user_stats.append({
                    'username': user.username,
                    'login_count': len(user_logins),
                    'last_login': last_login_time,
                    'avg_session_time': avg_user_session,
                    'peak_hour': peak_hour_str
                })
        
        # بيانات المخططات
        # تسجيلات الدخول حسب اليوم
        days_data = db.session.query(
            func.date(LoginLog.timestamp).label('day'),
            func.count().label('count')
        ).filter(
            LoginLog.action == 'login'
        ).group_by(
            func.date(LoginLog.timestamp)
        ).order_by(
            func.date(LoginLog.timestamp)
        ).limit(30).all()
        
        logins_by_day = {
            "labels": [day.strftime('%Y-%m-%d') for day, _ in days_data],
            "data": [count for _, count in days_data]
        }
        
        # تسجيلات الدخول حسب الساعة
        hours_data = db.session.query(
            func.extract('hour', LoginLog.timestamp).label('hour'),
            func.count().label('count')
        ).filter(
            LoginLog.action == 'login'
        ).group_by(
            func.extract('hour', LoginLog.timestamp)
        ).order_by(
            func.extract('hour', LoginLog.timestamp)
        ).all()
        
        logins_by_hour = {
            "labels": [f"{int(hour)}:00" for hour, _ in hours_data],
            "data": [count for _, count in hours_data]
        }
        
        # تسجيلات الدخول حسب المستخدم
        user_logins_data = db.session.query(
            User.username,
            func.count().label('count')
        ).join(
            LoginLog, User.id == LoginLog.user_id
        ).filter(
            LoginLog.action == 'login'
        ).group_by(
            User.username
        ).order_by(
            func.count().desc()
        ).limit(10).all()
        
        user_logins_chart = {
            "labels": [username for username, _ in user_logins_data],
            "data": [count for _, count in user_logins_data]
        }
        
        # تحديد ما إذا كان Elasticsearch متاحًا
        es_enabled = False
        try:
            from app import es_enabled
        except ImportError:
            pass
        
        return render_template(
            'reports/login_history.html',
            logs=logs,
            pagination=pagination,
            users=users,
            selected_user_id=user_id,
            selected_action=action,
            selected_start_date=start_date_str,
            selected_end_date=end_date_str,
            total_logins=total_logins,
            total_logouts=total_logouts,
            total_users=total_users,
            user_stats=user_stats,
            avg_session_time=avg_session_time,
            logins_by_day=logins_by_day,
            logins_by_hour=logins_by_hour,
            user_logins_chart=user_logins_chart,
            es_enabled=es_enabled,
            today_only=False
        )
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        print(f"Error generating login history report: {e}")
        return redirect(url_for('reports.index'))

# Add this new route to display login history from today
@reports_bp.route('/login_history/today')
@login_required
def login_history_today():
    """عرض تقرير سجل تسجيل الدخول والخروج لليوم الحالي"""
    try:
        # التحقق من صلاحيات المستخدم (فقط المدير يمكنه الوصول)
        if current_user.role != 'مدير':
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('dashboard'))
        
        # الحصول على تاريخ اليوم
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # بناء الاستعلام الأساسي
        base_query = LoginLog.query.join(User, LoginLog.user_id == User.id)
        
        # تصفية السجلات بدءًا من اليوم
        query = base_query.filter(LoginLog.timestamp >= today)
        
        # ترتيب النتائج حسب التاريخ (الأحدث أولاً)
        logs = query.order_by(LoginLog.timestamp.desc()).all()
        
        # الحصول على قائمة المستخدمين للفلتر
        users = User.query.all()
        
        # حساب الإحصائيات لليوم الحالي
        total_logins = base_query.filter(LoginLog.action == 'login', LoginLog.timestamp >= today).count()
        total_logouts = base_query.filter(LoginLog.action == 'logout', LoginLog.timestamp >= today).count()
        
        # إجمالي عدد المستخدمين
        total_users = User.query.count()
        
        # حساب متوسط مدة الجلسة لليوم الحالي
        sessions = []
        for user in users:
            user_logs = [log for log in logs if log.user_id == user.id]
            login_logs = [log for log in user_logs if log.action == 'login']
            logout_logs = [log for log in user_logs if log.action == 'logout']
            
            for login in login_logs:
                # البحث عن تسجيل خروج مطابق
                matching_logout = next((logout for logout in logout_logs if logout.timestamp > login.timestamp), None)
                if matching_logout:
                    duration = (matching_logout.timestamp - login.timestamp).total_seconds() // 60
                    sessions.append(duration)
        
        avg_session_time = f"{int(sum(sessions) / len(sessions)) if sessions else 0} دقيقة"
        
        # إحصائيات المستخدمين
        user_stats = []
        for user in users:
            user_logs = [log for log in logs if log.user_id == user.id]
            login_logs = [log for log in user_logs if log.action == 'login']
            
            if login_logs:
                last_login = max(login_logs, key=lambda x: x.timestamp)
                last_login_time = last_login.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                
                # حساب متوسط مدة الجلسة
                user_sessions = []
                user_login_logs = {}
                
                for log in LoginLog.query.filter_by(user_id=user.id, action='login').all():
                    if log.id not in user_login_logs:
                        user_login_logs[log.id] = log
                
                for log in LoginLog.query.filter_by(user_id=user.id, action='logout').all():
                    for login_id, login_log in list(user_login_logs.items()):
                        if login_log.timestamp < log.timestamp:
                            duration = (log.timestamp - login_log.timestamp).total_seconds() // 60
                            user_sessions.append(duration)
                            del user_login_logs[login_id]
                            break
                
                avg_user_session = f"{int(sum(user_sessions) / len(user_sessions)) if user_sessions else 0} دقيقة"
                
                # تحديد ساعة الذروة
                login_hours = [log.timestamp.hour for log in login_logs]
                peak_hour = max(set(login_hours), key=login_hours.count) if login_hours else 0
                peak_hour_str = f"{peak_hour}:00 - {peak_hour+1}:00"
                
                user_stats.append({
                    'username': user.username,
                    'login_count': len(login_logs),
                    'last_login': last_login_time,
                    'avg_session_time': avg_user_session,
                    'peak_hour': peak_hour_str
                })
        
        # بيانات المخططات
        logins_by_hour = {"labels": [], "data": []}
        for hour in range(24):
            hour_logins = len([log for log in logs if log.action == 'login' and log.timestamp.hour == hour])
            logins_by_hour["labels"].append(f"{hour}:00")
            logins_by_hour["data"].append(hour_logins)
        
        user_logins_chart = {"labels": [], "data": []}
        for stat in user_stats:
            if stat['login_count'] > 0:
                user_logins_chart["labels"].append(stat['username'])
                user_logins_chart["data"].append(stat['login_count'])
        
        # تحديد ما إذا كان Elasticsearch متاحًا
        es_enabled = False
        try:
            from app import es_enabled
        except ImportError:
            pass
        
        return render_template(
            'reports/login_history.html',
            logs=logs,
            users=users,
            selected_user_id=None,
            selected_action=None,
            selected_start_date=today.strftime('%Y-%m-%d'),
            selected_end_date=datetime.now().strftime('%Y-%m-%d'),
            total_logins=total_logins,
            total_logouts=total_logouts,
            total_users=total_users,
            user_stats=user_stats,
            avg_session_time=avg_session_time,
            logins_by_day={"labels": [today.strftime('%Y-%m-%d')], "data": [total_logins]},
            logins_by_hour=logins_by_hour,
            user_logins_chart=user_logins_chart,
            es_enabled=es_enabled,
            today_only=True
        )
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        print(f"Error generating today's login history report: {e}")
        return redirect(url_for('reports.index'))
