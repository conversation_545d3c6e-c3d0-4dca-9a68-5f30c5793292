{% extends "base.html" %}

{% block title %}بيانات الطبيب{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">بيانات الطبيب: {{ doctor.name }}</h5>
        <div>
            <a href="{{ url_for('doctors.edit', doctor_id=doctor.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <a href="{{ url_for('doctors.delete_confirm', doctor_id=doctor.id) }}" class="btn btn-danger">
                <i class="fas fa-trash-alt me-1"></i> حذف
            </a>
            <a href="{{ url_for('doctors.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">الاسم:</h6>
                <p>{{ doctor.name }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">التخصص:</h6>
                <p>{{ doctor.specialization }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">رقم الهاتف:</h6>
                <p>{{ doctor.phone or 'غير متوفر' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">البريد الإلكتروني:</h6>
                <p>{{ doctor.email or 'غير متوفر' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">سنوات الخبرة:</h6>
                <p>{{ doctor.experience_years or 'غير متوفر' }}</p>
            </div>
            <div class="col-md-12 mb-3">
                <h6 class="fw-bold">جدول العمل:</h6>
                <p>{{ doctor.work_schedule or 'غير متوفر' }}</p>
            </div>
        </div>

        <!-- Doctor appointments section -->
        <div class="mt-4">
            <h5 class="border-bottom pb-2">المواعيد</h5>
            {% if doctor.appointments %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>المريض</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for appointment in doctor.appointments|sort(attribute='appointment_datetime', reverse=True) %}
                            <tr>
                                <td>{{ appointment.patient.name }}</td>
                                <td>{{ appointment.appointment_datetime.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>{{ appointment.status }}</td>
                                <td>
                                    <!-- Delete these lines that use the incorrect endpoint -->
                                    <a href="{{ url_for('appointments.details', appointment_id=appointment.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-muted">لا توجد مواعيد مسجلة لهذا الطبيب</p>
            {% endif %}
        </div>

        <!-- Doctor treatments section -->
        <div class="mt-4">
            <h5 class="border-bottom pb-2">العلاجات</h5>
            {% if doctor.treatments %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>المريض</th>
                                <th>الدواء</th>
                                <th>الجرعة</th>
                                <th>تاريخ البدء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for treatment in doctor.treatments|sort(attribute='created_at', reverse=True) %}
                            <tr>
                                <td>{{ treatment.patient.name }}</td>
                                <td>{{ treatment.medication_name }}</td>
                                <td>{{ treatment.dosage }}</td>
                                <td>{{ treatment.start_date.strftime('%Y-%m-%d') if treatment.start_date else 'غير محدد' }}</td>
                                <td>
                                    <a href="{{ url_for('treatments.view', treatment_id=treatment.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-muted">لا توجد علاجات مسجلة لهذا الطبيب</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}