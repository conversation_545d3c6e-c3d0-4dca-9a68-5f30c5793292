{% extends "base.html" %}

{% block page_title %}إضافة طبيب جديد{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">بيانات الطبيب الجديد</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('doctors.add') }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="specialization" class="form-label">التخصص <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="specialization" name="specialization" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="phone" name="phone">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" name="email">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="experience_years" class="form-label">سنوات الخبرة</label>
                    <input type="number" class="form-control" id="experience_years" name="experience_years" min="0">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="work_schedule" class="form-label">مواعيد العمل</label>
                    <textarea class="form-control" id="work_schedule" name="work_schedule" rows="2"></textarea>
                </div>
                
                <hr class="my-4">
                <h5>بيانات تسجيل الدخول</h5>
                
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('doctors.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ البيانات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}