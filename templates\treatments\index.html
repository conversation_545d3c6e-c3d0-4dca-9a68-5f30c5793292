{% extends "base.html" %}

{% block page_title %}العلاجات{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة العلاجات</h5>
        <a href="{{ url_for('treatments.add') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة علاج جديد
        </a>
    </div>
    
    <!-- Search Box -->
    <div class="card-body border-bottom">
        <form method="GET" action="{{ url_for('treatments.index') }}">
            <div class="input-group">
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="ابحث عن اسم المريض، رقم الهاتف، أو رقم المريض..." 
                       value="{{ request.args.get('search', '') }}">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i> بحث
                </button>
                {% if request.args.get('search') %}
                <a href="{{ url_for('treatments.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i> مسح
                </a>
                {% endif %}
            </div>
        </form>
    </div>
    
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>المريض</th>
                        <th>العلاج</th>
                        <th>تاريخ البدء</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for treatment in treatments %}
                    <tr>
                        <td>{{ treatment.id }}</td>
                        <td>{{ treatment.patient.name }}</td>
                        <td>{{ treatment.treatment_name }}</td>
                        <td>{{ treatment.start_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if treatment.status == 'active' %}
                            <span class="badge bg-success">نشط</span>
                            {% elif treatment.status == 'completed' %}
                            <span class="badge bg-info">مكتمل</span>
                            {% elif treatment.status == 'cancelled' %}
                            <span class="badge bg-danger">ملغي</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ treatment.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{{ url_for('treatments.view', treatment_id=treatment.id) }}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ url_for('treatments.edit', treatment_id=treatment.id) }}" class="btn btn-sm btn-warning">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="#" onclick="confirmDelete('{{ url_for('treatments.delete', treatment_id=treatment.id) }}')" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center">لا توجد علاجات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا العلاج؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">حذف</a>
            </div>
        </div>
    </div>
</div>

<script>
    function confirmDelete(deleteUrl) {
        document.getElementById('confirmDeleteBtn').href = deleteUrl;
        var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }
</script>
{% endblock %}