{% extends "base_print.html" %}

{% block page_title %}فاتورة #{{ invoice.id }}{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row mb-4">
        <div class="col-6 text-start">
            <h2>فاتورة #{{ invoice.id }}</h2>
            <p>تاريخ الفاتورة: {{ invoice.invoice_date.strftime('%Y-%m-%d') }}</p>
            {% if invoice.due_date %}
            <p>تاريخ الاستحقاق: {{ invoice.due_date.strftime('%Y-%m-%d') }}</p>
            {% endif %}
        </div>
        <div class="col-6 text-end">
            <h3>عيادة المهندس أحمد السنوسي</h3>
            <p>العنوان: طرابلس</p>
            <p>الهاتف: **********</p>
            <p>البريد الإلكتروني: <EMAIL></p>
        </div>
    </div>

    <hr>

    <div class="row mb-4">
        <div class="col-6">
            <h5>بيانات المريض:</h5>
            <p><strong>الاسم:</strong> {{ invoice.patient.name }}</p>
            <p><strong>رقم الهوية:</strong> {{ invoice.patient.id_number }}</p>
            <p><strong>رقم الهاتف:</strong> {{ invoice.patient.phone }}</p>
        </div>
        <div class="col-6">
            <h5>حالة الفاتورة:</h5>
            <p>
                {% if invoice.status == 'مدفوعة' %}
                    <span class="badge bg-success">{{ invoice.status }}</span>
                {% elif invoice.status == 'غير مدفوعة' %}
                    <span class="badge bg-danger">{{ invoice.status }}</span>
                {% else %}
                    <span class="badge bg-warning">{{ invoice.status }}</span>
                {% endif %}
            </p>
            <!-- إضافة اسم الطبيب هنا -->
            <p><strong>الطبيب المعالج:</strong> {{ invoice.doctor.name }}{% if invoice.doctor.specialization %} - {{ invoice.doctor.specialization }}{% endif %}</p>
            {% if invoice.payment_method %}
            <p><strong>طريقة الدفع:</strong> {{ invoice.payment_method }}</p>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <h5>تفاصيل الفاتورة:</h5>
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>الوصف</th>
                        <th class="text-end">المبلغ</th>
                    </tr>
                </thead>
                <tbody>
                    {% if invoice.appointment %}
                    <tr>
                        <td>موعد بتاريخ {{ invoice.appointment.appointment_datetime.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td class="text-end">{{ invoice.amount }} ر.س</td>
                    </tr>
                    {% elif invoice.treatment %}
                    <tr>
                        <td>علاج: {{ invoice.treatment.medication_name }}</td>
                        <td class="text-end">{{ invoice.amount }} ر.س</td>
                    </tr>
                    {% else %}
                    <tr>
                        <td>خدمات طبية</td>
                        <td class="text-end">{{ invoice.amount }} ر.س</td>
                    </tr>
                    {% endif %}
                    
                    {% if invoice.tax > 0 %}
                    <tr>
                        <td>ضريبة القيمة المضافة</td>
                        <td class="text-end">{{ invoice.tax }} ر.س</td>
                    </tr>
                    {% endif %}
                    
                    {% if invoice.discount > 0 %}
                    <tr>
                        <td>خصم</td>
                        <td class="text-end">- {{ invoice.discount }} ر.س</td>
                    </tr>
                    {% endif %}
                </tbody>
                <tfoot>
                    <tr class="table-primary">
                        <th>المبلغ الإجمالي</th>
                        <th class="text-end">{{ invoice.total_amount }} ر.س</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    {% if invoice.notes %}
    <div class="row mt-4">
        <div class="col-12">
            <h5>ملاحظات:</h5>
            <p>{{ invoice.notes }}</p>
        </div>
    </div>
    {% endif %}

    <div class="row mt-5">
        <div class="col-6">
            <p><strong>ملاحظة:</strong> هذه الفاتورة صالحة لمدة 30 يوماً من تاريخ الإصدار.</p>
        </div>
        <div class="col-6 text-end">
            <p>توقيع المسؤول: ________________</p>
        </div>
    </div>
    
    <div class="row mt-5">
        <div class="col-12 text-center">
            <p class="small">شكراً لثقتكم بنا -   عيادة المهندس أحمد السنوسي  ا</p>
        </div>
    </div>
</div>

<div class="d-print-none mt-4 text-center">
    <button class="btn btn-primary" onclick="window.print()">
        <i class="fas fa-print me-1"></i> طباعة الفاتورة
    </button>
    <a href="{{ url_for('invoices.view', invoice_id=invoice.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i> رجوع
    </a>
</div>
{% endblock %}