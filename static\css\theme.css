/* ألوان الوضع النهاري (الافتراضي) */
:root,
[data-bs-theme="light"] {
    --primary-bg: #f8f9fa;
    --card-bg: #ffffff;
    --card-header-bg: #f0f0f0;
    --text-color: #212529;
    --border-color: #dee2e6;
    --sidebar-bg: #343a40;
    --sidebar-text: rgba(255, 255, 255, 0.75);
    --sidebar-active: #0d6efd;
    --table-stripe: rgba(0, 0, 0, 0.05);
    --input-bg: #fff;
    --input-border: #ced4da;
    --primary-btn: #0d6efd;
    --primary-btn-hover: #0b5ed7;
    --success-btn: #198754;
    --success-btn-hover: #157347;
    --danger-btn: #dc3545;
    --danger-btn-hover: #bb2d3b;
    --info-btn: #0dcaf0;
    --info-btn-hover: #31d2f2;
    --warning-btn: #ffc107;
    --warning-btn-hover: #ffca2c;
    --secondary-btn: #6c757d;
    --secondary-btn-hover: #5c636a;
    --badge-bg: #f8f9fa;
    --badge-text: #212529;
    --hover-bg: #f8f9fa;
    --shadow-color: rgba(0, 0, 0, 0.15);
    --chart-colors: #0d6efd, #20c997, #ffc107, #dc3545, #6610f2;
}

/* ألوان الوضع الليلي */
[data-bs-theme="dark"] {
    --primary-bg: #212529;
    --card-bg: #2c3034;
    --card-header-bg: #343a40;
    --text-color: #f8f9fa;
    --border-color: #495057;
    --sidebar-bg: #1a1d20;
    --sidebar-text: rgba(255, 255, 255, 0.55);
    --sidebar-active: #0d6efd;
    --table-stripe: rgba(255, 255, 255, 0.05);
    --input-bg: #2c3034;
    --input-border: #495057;
    --primary-btn: #0d6efd;
    --primary-btn-hover: #0a58ca;
    --success-btn: #198754;
    --success-btn-hover: #146c43;
    --danger-btn: #dc3545;
    --danger-btn-hover: #b02a37;
    --info-btn: #0dcaf0;
    --info-btn-hover: #31d2f2;
    --warning-btn: #ffc107;
    --warning-btn-hover: #ffca2c;
    --secondary-btn: #6c757d;
    --secondary-btn-hover: #565e64;
    --badge-bg: #343a40;
    --badge-text: #f8f9fa;
    --hover-bg: #343a40;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --chart-colors: #0d6efd, #20c997, #ffc107, #dc3545, #6610f2;
}

/* تطبيق المتغيرات على العناصر */
body {
    background-color: var(--primary-bg);
    color: var(--text-color);
    transition: background-color 0.3s, color 0.3s;
}

.card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem var(--shadow-color);
}

.card-header {
    background-color: var(--card-header-bg);
    border-color: var(--border-color);
}

.sidebar {
    background-color: var(--sidebar-bg);
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(-5px);
}

.sidebar .nav-link.active {
    background-color: var(--sidebar-active);
    font-weight: bold;
}

.table {
    color: var(--text-color);
    border-color: var(--border-color);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--table-stripe);
}

.table-hover tbody tr:hover {
    background-color: var(--hover-bg);
}

.form-control, .form-select {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    background-color: var(--input-bg);
    color: var(--text-color);
    border-color: var(--primary-btn);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تخصيص الأزرار */
.btn-primary {
    background-color: var(--primary-btn);
    border-color: var(--primary-btn);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-btn-hover);
    border-color: var(--primary-btn-hover);
}

.btn-success {
    background-color: var(--success-btn);
    border-color: var(--success-btn);
}

.btn-success:hover, .btn-success:focus {
    background-color: var(--success-btn-hover);
    border-color: var(--success-btn-hover);
}

.btn-danger {
    background-color: var(--danger-btn);
    border-color: var(--danger-btn);
}

.btn-danger:hover, .btn-danger:focus {
    background-color: var(--danger-btn-hover);
    border-color: var(--danger-btn-hover);
}

.btn-info {
    background-color: var(--info-btn);
    border-color: var(--info-btn);
}

.btn-info:hover, .btn-info:focus {
    background-color: var(--info-btn-hover);
    border-color: var(--info-btn-hover);
}

.btn-warning {
    background-color: var(--warning-btn);
    border-color: var(--warning-btn);
}

.btn-warning:hover, .btn-warning:focus {
    background-color: var(--warning-btn-hover);
    border-color: var(--warning-btn-hover);
}

.btn-secondary {
    background-color: var(--secondary-btn);
    border-color: var(--secondary-btn);
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: var(--secondary-btn-hover);
    border-color: var(--secondary-btn-hover);
}

/* تخصيص الشارات */
.badge {
    transition: all 0.2s ease;
}

.badge-primary, .bg-primary {
    background-color: var(--primary-btn) !important;
}

.badge-success, .bg-success {
    background-color: var(--success-btn) !important;
}

.badge-danger, .bg-danger {
    background-color: var(--danger-btn) !important;
}

.badge-info, .bg-info {
    background-color: var(--info-btn) !important;
}

.badge-warning, .bg-warning {
    background-color: var(--warning-btn) !important;
}

.badge-secondary, .bg-secondary {
    background-color: var(--secondary-btn) !important;
}

/* تخصيص زر تبديل الوضع */
.theme-toggle {
    transition: all 0.3s;
    border-radius: 20px;
    padding: 0.375rem 0.75rem;
}

[data-bs-theme="dark"] .theme-toggle {
    background-color: #495057;
    color: #f8f9fa;
}

[data-bs-theme="light"] .theme-toggle {
    background-color: #e9ecef;
    color: #212529;
}

/* تحسينات إضافية للجداول */
.table th {
    font-weight: bold;
    border-bottom-width: 2px;
}

.table-responsive {
    border-radius: 0.25rem;
    overflow: hidden;
}

/* تحسينات للرسوم البيانية */
.chart-container {
    background-color: var(--card-bg);
    border-radius: 0.25rem;
    padding: 1rem;
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
}

/* تحسينات للتنقل بين الصفحات */
.pagination .page-link {
    color: var(--primary-btn);
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-btn);
    border-color: var(--primary-btn);
    color: white;
}

.pagination .page-link:hover {
    background-color: var(--hover-bg);
    border-color: var(--border-color);
}

/* تحسينات للنوافذ المنبثقة */
.modal-content {
    background-color: var(--card-bg);
    color: var(--text-color);
}

.modal-header {
    background-color: var(--card-header-bg);
    border-color: var(--border-color);
}

.modal-footer {
    border-color: var(--border-color);
}

/* تحسينات للتنبيهات */
.alert {
    border: none;
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
}

/* تأثيرات حركية للعناصر */
.card, .btn, .badge, .alert, .nav-link {
    transition: all 0.3s ease;
}

/* تحسينات لعرض البيانات */
.data-container {
    padding: 1rem;
    border-radius: 0.25rem;
    background-color: var(--card-bg);
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
    margin-bottom: 1rem;
}

/* تحسينات للقوائم */
.list-group-item {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
}

.list-group-item.active {
    background-color: var(--primary-btn);
    border-color: var(--primary-btn);
}