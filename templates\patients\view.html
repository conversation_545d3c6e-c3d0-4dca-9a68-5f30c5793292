{% extends "base.html" %}

{% block title %}تفاصيل المريض{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تفاصيل المريض #{{ patient.id }}</h5>
        <div>
            <a href="{{ url_for('patients.edit', patient_id=patient.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <a href="{{ url_for('patients.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">الاسم الكامل:</h6>
                <p>{{ patient.name }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ الميلاد:</h6>
                <p>{{ patient.birth_date.strftime('%Y-%m-%d') }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">الجنس:</h6>
                <p>{{ patient.gender }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">فصيلة الدم:</h6>
                <p>{{ patient.blood_type or 'غير محدد' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">رقم الهاتف:</h6>
                <p>{{ patient.phone or 'غير متوفر' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">البريد الإلكتروني:</h6>
                <p>{{ patient.email or 'غير متوفر' }}</p>
            </div>
            <div class="col-md-12 mb-3">
                <h6 class="fw-bold">العنوان:</h6>
                <p>{{ patient.address or 'غير متوفر' }}</p>
            </div>
            <div class="col-md-12 mb-3">
                <h6 class="fw-bold">التاريخ الطبي:</h6>
                <p>{{ patient.medical_history or 'لا يوجد' }}</p>
            </div>
            <div class="col-md-12 mb-3">
                <h6 class="fw-bold">الحساسية:</h6>
                <p>{{ patient.allergies or 'لا يوجد' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ التسجيل:</h6>
                <p>{{ patient.created_at.strftime('%Y-%m-%d') }}</p>
            </div>
        </div>

        <!-- Patient appointments section -->
        <div class="mt-4">
            <h5 class="border-bottom pb-2">المواعيد</h5>
            {% if patient.appointments %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الوقت</th>
                                <th>الحالة</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for appointment in patient.appointments|sort(attribute='appointment_datetime', reverse=True) %}
                            <tr>
                                <td>{{ appointment.appointment_datetime.strftime('%Y-%m-%d') }}</td>
                                <td>{{ appointment.appointment_datetime.strftime('%H:%M') }}</td>
                                <td>{{ appointment.status }}</td>
                                <td>{{ appointment.notes|truncate(30) if appointment.notes else '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-muted">لا توجد مواعيد مسجلة لهذا المريض</p>
            {% endif %}
        </div>

        <!-- Patient medical tests section -->
        <div class="mt-4">
            <h5 class="border-bottom pb-2">الفحوصات الطبية</h5>
            {% if patient.medical_tests %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>اسم الفحص</th>
                                <th>التاريخ</th>
                                <th>النتائج</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for test in patient.medical_tests|sort(attribute='test_date', reverse=True) %}
                            <tr>
                                <td>{{ test.test_name }}</td>
                                <td>{{ test.test_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ test.results|truncate(30) if test.results else 'غير متوفر' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-muted">لا توجد فحوصات طبية مسجلة لهذا المريض</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}