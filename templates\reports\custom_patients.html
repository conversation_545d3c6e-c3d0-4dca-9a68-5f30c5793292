{% extends "base.html" %}

{% block page_title %}تقرير مخصص - المرضى{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير المرضى المخصص</h5>
        <div>
            <a href="{{ url_for('reports.custom_report') }}" class="btn btn-secondary btn-sm me-2">
                <i class="fas fa-edit me-1"></i> تعديل التقرير
            </a>
            <a href="{{ url_for('reports.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h5>معلومات التقرير:</h5>
            <p>
                <strong>نوع التقرير:</strong> تقرير المرضى<br>
                <strong>الفترة:</strong> 
                {% if start_date %}من {{ start_date }}{% endif %}
                {% if end_date %} إلى {{ end_date }}{% endif %}
                {% if not start_date and not end_date %}كل الفترات{% endif %}
            </p>
        </div>
        
        <div class="d-flex justify-content-between mb-3">
            <h5>عدد المرضى: {{ results|length }}</h5>
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> طباعة التقرير
            </button>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المريض</th>
                        <th>تاريخ الميلاد</th>
                        <th>الجنس</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>فصيلة الدم</th>
                        <th>تاريخ التسجيل</th>
                    </tr>
                </thead>
                <tbody>
                    {% for patient in results %}
                    <tr>
                        <td>{{ patient.id }}</td>
                        <td>
                            <a href="{{ url_for('patients.view', patient_id=patient.id) }}">
                                {{ patient.name }}
                            </a>
                        </td>
                        <td>{{ patient.birth_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ patient.gender }}</td>
                        <td>{{ patient.phone }}</td>
                        <td>{{ patient.email }}</td>
                        <td>{{ patient.blood_type }}</td>
                        <td>{{ patient.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}