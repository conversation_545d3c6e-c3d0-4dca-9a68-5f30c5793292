{% extends "base.html" %}

{% block page_title %}تقرير المرضى{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير المرضى</h5>
        <div>
            <a href="{{ url_for('reports') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>من تاريخ:</strong> {{ date_from if date_from else 'غير محدد' }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>إلى تاريخ:</strong> {{ date_to if date_to else 'غير محدد' }}</p>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>تاريخ الميلاد</th>
                        <th>الجنس</th>
                        <th>رقم الهاتف</th>
                        <th>فصيلة الدم</th>
                        <th>تاريخ التسجيل</th>
                    </tr>
                </thead>
                <tbody>
                    {% if patients %}
                        {% for patient in patients %}
                        <tr>
                            <td>{{ patient.id }}</td>
                            <td>{{ patient.name }}</td>
                            <td>{{ patient.birth_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ patient.gender }}</td>
                            <td>{{ patient.phone }}</td>
                            <td>{{ patient.blood_type }}</td>
                            <td>{{ patient.created_at.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="text-center">لا يوجد مرضى مسجلين في هذه الفترة</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <div class="mt-3">
            <p><strong>إجمالي عدد المرضى:</strong> {{ patients|length }}</p>
        </div>
    </div>
</div>
{% endblock %}