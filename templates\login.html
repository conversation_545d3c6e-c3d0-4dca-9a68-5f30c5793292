{% extends "base.html" %}

{% block auth_content %}
<div class="row justify-content-center align-items-center min-vh-100 py-5">
    <div class="col-md-8 col-lg-6 col-xl-5">
        <div class="card shadow-lg border-0 rounded-lg">
            <div class="card-header bg-gradient-primary-to-secondary text-white text-center py-4">
                <h3 class="mb-0 fw-bold">
                    <i class="fas fa-hospital-alt me-2"></i>نظام إدارة العيادة
                </h3>
            </div>
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <h4 class="text-primary fw-bold">مرحباً بك</h4>
                    <p class="text-muted">قم بتسجيل الدخول للوصول إلى لوحة التحكم</p>
                </div>
                
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST" action="{{ url_for('login') }}" class="needs-validation" novalidate>
                    <div class="form-floating mb-4">
                        <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required autofocus>
                        <label for="username">
                            <i class="fas fa-user text-primary me-1"></i>
                            اسم المستخدم
                        </label>
                        <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                    </div>
                    
                    <div class="form-floating mb-4">
                        <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                        <label for="password">
                            <i class="fas fa-lock text-primary me-1"></i>
                            كلمة المرور
                        </label>
                        <div class="invalid-feedback">يرجى إدخال كلمة المرور</div>
                    </div>
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            تذكرني على هذا الجهاز
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg fw-bold">
                            <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer bg-light text-center py-3">
                <div class="small">
                    <i class="far fa-copyright me-1"></i>
                    {{ current_year }} نظام إدارة العيادة | جميع الحقوق محفوظة
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-gradient-primary-to-secondary {
        background: linear-gradient(135deg, #4e73df 0%, #36b9cc 100%);
    }
    
    .card {
        transition: all 0.3s ease-in-out;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
    }
    
    .form-floating input:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }
    
    .btn-primary {
        background-color: #4e73df;
        border-color: #4e73df;
        transition: all 0.2s;
    }
    
    .btn-primary:hover {
        background-color: #2e59d9;
        border-color: #2653d4;
        transform: translateY(-2px);
    }
    
    .btn-primary:active {
        transform: translateY(0);
    }
</style>

<script>
    // تفعيل التحقق من صحة النموذج
    (function() {
        'use strict';
        
        // استهداف جميع النماذج التي تحتوي على الفئة needs-validation
        var forms = document.querySelectorAll('.needs-validation');
        
        // حلقة لمنع الإرسال
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                
                form.classList.add('was-validated');
            }, false);
        });
    })();
</script>
{% endblock %}