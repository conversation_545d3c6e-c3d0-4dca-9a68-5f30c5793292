{% extends "base.html" %}

{% block title %}تفاصيل الموعد{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تفاصيل الموعد</h5>
        <div>
            <a href="{{ url_for('appointments.edit', appointment_id=appointment.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <a href="{{ url_for('appointments.delete_confirm', appointment_id=appointment.id) }}" class="btn btn-danger">
                <i class="fas fa-trash-alt me-1"></i> حذف
            </a>
            <a href="{{ url_for('appointments.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">المريض:</h6>
                <p>{{ appointment.patient.name }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">الطبيب:</h6>
                <p>{{ appointment.doctor.name }} - {{ appointment.doctor.specialization }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ ووقت الموعد:</h6>
                <p>{{ appointment.appointment_datetime.strftime('%Y-%m-%d %H:%M') }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">حالة الموعد:</h6>
                <p>
                    {% if appointment.status == 'مؤكد' %}
                        <span class="badge bg-success">{{ appointment.status }}</span>
                    {% elif appointment.status == 'ملغي' %}
                        <span class="badge bg-danger">{{ appointment.status }}</span>
                    {% elif appointment.status == 'منجز' %}
                        <span class="badge bg-info">{{ appointment.status }}</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ appointment.status }}</span>
                    {% endif %}
                </p>
            </div>
            <div class="col-md-12 mb-3">
                <h6 class="fw-bold">ملاحظات:</h6>
                <p>{{ appointment.notes or 'لا توجد ملاحظات' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ الإنشاء:</h6>
                <p>{{ appointment.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
            </div>
        </div>

        <!-- Related invoices section if any -->
        {% if appointment.invoice %}
        <div class="mt-4">
            <h5 class="border-bottom pb-2">الفاتورة المرتبطة</h5>
            <div class="table-responsive">
                <table class="table table-sm table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ appointment.invoice.id }}</td>
                            <td>{{ appointment.invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ appointment.invoice.total_amount }} د.ل</td>
                            <td>{{ appointment.invoice.status }}</td>
                            <td>
                                <a href="{{ url_for('invoices.view', invoice_id=appointment.invoice.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}