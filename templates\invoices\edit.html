{% extends "base.html" %}

{% block page_title %}تعديل الفاتورة{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تعديل بيانات الفاتورة</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('invoices.edit', invoice_id=invoice.id) }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="patient_id" class="form-label">المريض <span class="text-danger">*</span></label>
                    <select class="form-select" id="patient_id" name="patient_id" required>
                        <option value="" disabled>اختر المريض</option>
                        {% for patient in patients %}
                        <option value="{{ patient.id }}" {% if patient.id == invoice.patient_id %}selected{% endif %}>{{ patient.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="doctor_id" class="form-label">الطبيب <span class="text-danger">*</span></label>
                    <select class="form-select" id="doctor_id" name="doctor_id" required>
                        <option value="" disabled>اختر الطبيب</option>
                        {% for doctor in doctors %}
                        <option value="{{ doctor.id }}" {% if doctor.id == invoice.doctor_id %}selected{% endif %}>{{ doctor.name }} - {{ doctor.specialization }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="appointment_id" class="form-label">الموعد (اختياري)</label>
                    <select class="form-select" id="appointment_id" name="appointment_id">
                        <option value="">بدون موعد</option>
                        {% for appointment in appointments %}
                        <option value="{{ appointment.id }}" {% if appointment.id == invoice.appointment_id %}selected{% endif %}>{{ appointment.appointment_datetime.strftime('%Y-%m-%d %H:%M') }} - {{ appointment.patient.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="treatment_id" class="form-label">العلاج (اختياري)</label>
                    <select class="form-select" id="treatment_id" name="treatment_id">
                        <option value="">بدون علاج</option>
                        {% for treatment in treatments %}
                        <option value="{{ treatment.id }}" {% if invoice.treatment_id == treatment.id %}selected{% endif %}>
                            {{ treatment.medication_name }} - {{ treatment.patient.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="invoice_date" class="form-label">تاريخ الفاتورة <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="invoice_date" name="invoice_date" 
                           value="{{ invoice.invoice_date.strftime('%Y-%m-%d') }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                    <input type="date" class="form-control" id="due_date" name="due_date" 
                           value="{{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '' }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="number" step="0.01" class="form-control" id="amount" name="amount" 
                               value="{{ invoice.amount }}" required>
                        <span class="input-group-text">د.ل</span>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="tax" class="form-label">الضريبة</label>
                    <div class="input-group">
                        <input type="number" step="0.01" class="form-control" id="tax" name="tax" 
                               value="{{ invoice.tax }}">
                        <span class="input-group-text">د.ل</span>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="discount" class="form-label">الخصم</label>
                    <div class="input-group">
                        <input type="number" step="0.01" class="form-control" id="discount" name="discount" 
                               value="{{ invoice.discount }}">
                        <span class="input-group-text">د.ل</span>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="status" class="form-label">حالة الفاتورة <span class="text-danger">*</span></label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="غير مدفوعة" {% if invoice.status == 'غير مدفوعة' %}selected{% endif %}>غير مدفوعة</option>
                        <option value="مدفوعة" {% if invoice.status == 'مدفوعة' %}selected{% endif %}>مدفوعة</option>
                        <option value="مدفوعة جزئياً" {% if invoice.status == 'مدفوعة جزئياً' %}selected{% endif %}>مدفوعة جزئياً</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="payment_method" class="form-label">طريقة الدفع</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="">اختر طريقة الدفع</option>
                        <option value="نقداً" {% if invoice.payment_method == 'نقداً' %}selected{% endif %}>نقداً</option>
                        <option value="بطاقة ائتمان" {% if invoice.payment_method == 'بطاقة ائتمان' %}selected{% endif %}>بطاقة ائتمان</option>
                        <option value="تحويل بنكي" {% if invoice.payment_method == 'تحويل بنكي' %}selected{% endif %}>تحويل بنكي</option>
                        <option value="أخرى" {% if invoice.payment_method == 'أخرى' %}selected{% endif %}>أخرى</option>
                    </select>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ invoice.notes }}</textarea>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('invoices.view', invoice_id=invoice.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}