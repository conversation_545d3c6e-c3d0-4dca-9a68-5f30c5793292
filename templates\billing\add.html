{% extends "base.html" %}

{% block page_title %}إضافة فاتورة جديدة{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">بيانات الفاتورة الجديدة</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('add_bill') }}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="patient_id" class="form-label">المريض <span class="text-danger">*</span></label>
                    <select class="form-select" id="patient_id" name="patient_id" required>
                        <option value="" selected disabled>اختر المريض</option>
                        {% for patient in patients %}
                        <option value="{{ patient.id }}">{{ patient.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="bill_date" class="form-label">تاريخ الفاتورة <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="bill_date" name="bill_date" value="{{ today }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="total_amount" class="form-label">المبلغ الإجمالي <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="total_amount" name="total_amount" step="0.01" min="0" required>
                        <span class="input-group-text">ريال</span>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="payment_status" class="form-label">حالة الدفع</label>
                    <select class="form-select" id="payment_status" name="payment_status">
                        <option value="غير مدفوع" selected>غير مدفوع</option>
                        <option value="مدفوع">مدفوع</option>
                        <option value="جزئي">جزئي</option>
                    </select>
                </div>
                <div class="col-md-12 mb-3">
                    <label for="description" class="form-label">وصف الفاتورة</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('billing') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ الفاتورة
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}