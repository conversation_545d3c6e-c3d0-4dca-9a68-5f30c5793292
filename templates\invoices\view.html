{% extends "base.html" %}

{% block page_title %}تفاصيل الفاتورة{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تفاصيل الفاتورة #{{ invoice.id }}</h5>
        <div>
            <a href="{{ url_for('invoices.edit', invoice_id=invoice.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <a href="{{ url_for('invoices.print', invoice_id=invoice.id) }}" class="btn btn-info" target="_blank">
                <i class="fas fa-print me-1"></i> طباعة
            </a>
            <a href="{{ url_for('invoices.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">المريض:</h6>
                <p>{{ invoice.patient.name }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">حالة الفاتورة:</h6>
                <p>
                    {% if invoice.status == 'مدفوعة' %}
                        <span class="badge bg-success">{{ invoice.status }}</span>
                    {% elif invoice.status == 'غير مدفوعة' %}
                        <span class="badge bg-danger">{{ invoice.status }}</span>
                    {% else %}
                        <span class="badge bg-warning">{{ invoice.status }}</span>
                    {% endif %}
                </p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">الطبيب:</h6>
                <p>{{ invoice.doctor.name }}{% if invoice.doctor.specialization %} - {{ invoice.doctor.specialization }}{% endif %}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ الفاتورة:</h6>
                <p>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ الاستحقاق:</h6>
                <p>{{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '-' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">الموعد المرتبط:</h6>
                <p>
                    {% if invoice.appointment %}
                        {{ invoice.appointment.appointment_datetime.strftime('%Y-%m-%d') }}
                    {% else %}
                        -
                    {% endif %}
                </p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">العلاج المرتبط:</h6>
                <p>{{ invoice.treatment.medication_name if invoice.treatment else '-' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">طريقة الدفع:</h6>
                <p>{{ invoice.payment_method or '-' }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ الإنشاء:</h6>
                <p>{{ invoice.created_at.strftime('%Y-%m-%d') }}</p>
            </div>
            
            <div class="col-12">
                <hr>
                <h5 class="mb-3">تفاصيل المبلغ</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th style="width: 50%">المبلغ الأساسي:</th>
                                <td>{{ invoice.amount }} د.ل</td>
                            </tr>
                            <tr>
                                <th>الضريبة:</th>
                                <td>{{ invoice.tax }} د.ل</td>
                            </tr>
                            <tr>
                                <th>الخصم:</th>
                                <td>{{ invoice.discount }} د.ل</td>
                            </tr>
                            <tr class="table-primary">
                                <th>المبلغ الإجمالي:</th>
                                <td class="fw-bold">{{ invoice.total_amount }} د.ل</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            {% if invoice.notes %}
            <div class="col-12 mt-3">
                <h6 class="fw-bold">ملاحظات:</h6>
                <p>{{ invoice.notes }}</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}