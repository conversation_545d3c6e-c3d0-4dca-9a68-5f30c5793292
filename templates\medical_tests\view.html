{% extends "base.html" %}

{% block page_title %}تفاصيل الفحص الطبي{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تفاصيل الفحص الطبي #{{ test.id }}</h5>
        <div>
            <a href="#" onclick="printMedicalTest(); return false;" class="btn btn-info me-2">
                <i class="fas fa-print me-1"></i> طباعة الفحص
            </a>
            <a href="{{ url_for('medical_tests.edit', test_id=test.id) }}" class="btn btn-warning me-2">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <a href="{{ url_for('medical_tests.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> رجوع
            </a>
        </div>
    </div>
    <div class="card-body" id="printable-content">
        <div class="row">
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">المريض:</h6>
                <p>{{ test.patient.name }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">اسم الفحص:</h6>
                <p>{{ test.test_name }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ الفحص:</h6>
                <p>{{ test.test_date.strftime('%Y-%m-%d') }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="fw-bold">تاريخ الإنشاء:</h6>
                <p>{{ test.created_at.strftime('%Y-%m-%d') }}</p>
            </div>
            
            <div class="col-12 mb-3">
                <h6 class="fw-bold">نتائج الفحص:</h6>
                <p>{{ test.results or 'غير متوفر' }}</p>
            </div>
            
            <div class="col-12 mb-3">
                <h6 class="fw-bold">ملاحظات الطبيب:</h6>
                <p>{{ test.doctor_notes or '-' }}</p>
            </div>
        </div>
    </div>
</div>

</div>
</div>

<!-- Add print functionality script -->
<script>
    function printMedicalTest() {
        // Store the original page content
        const originalContent = document.body.innerHTML;
        
        // Get only the printable content
        const printContent = document.getElementById('printable-content').innerHTML;
        
        // Replace the body with just the content to be printed
        document.body.innerHTML = `
            <div class="container mt-4" dir="rtl">
                <div class="text-center mb-4">
                    <h2>تقرير الفحص الطبي</h2>
                    <h4>رقم الفحص: {{ test.id }}</h4>
                </div>
                ${printContent}
            </div>
        `;
        
        // Print the document
        window.print();
        
        // Restore the original content
        document.body.innerHTML = originalContent;
    }
</script>
{% endblock %}