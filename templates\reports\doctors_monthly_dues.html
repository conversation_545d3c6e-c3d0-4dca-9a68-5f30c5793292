{% extends "base.html" %}

{% block page_title %}مستحقات الأطباء الشهرية{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">مستحقات الأطباء الشهرية</h5>
        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-right me-1"></i> رجوع
        </a>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('reports.doctors_monthly_dues') }}" class="mb-4">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="doctor_id" class="form-label">الطبيب</label>
                    <select class="form-select select2" id="doctor_id" name="doctor_id">
                        <option value="" selected>جميع الأطباء</option>
                        {% for doctor in doctors %}
                        <option value="{{ doctor.id }}" {% if selected_doctor_id == doctor.id %}selected{% endif %}>{{ doctor.name }} - {{ doctor.specialization }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="month" class="form-label">الشهر</label>
                    <select class="form-select" id="month" name="month" required>
                        {% for i in range(1, 13) %}
                        <option value="{{ i }}" {% if selected_month == i %}selected{% endif %}>{{ i }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="year" class="form-label">السنة</label>
                    <select class="form-select" id="year" name="year" required>
                        {% for year in years %}
                        <option value="{{ year }}" {% if selected_year == year %}selected{% endif %}>{{ year }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">عرض</button>
                </div>
            </div>
        </form>

        {% if doctor_dues %}
        <div class="table-responsive mt-4">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الطبيب</th>
                        <th>التخصص</th>
                        <th>إجمالي الإيرادات</th>
                        <th>نسبة الطبيب</th>
                        <th>مستحقات الطبيب</th>
                        <th>مستحقات العيادة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in doctor_dues %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ item.doctor.name }}</td>
                        <td>{{ item.doctor.specialization }}</td>
                        <td>{{ item.total_revenue|round(2) }} ر.س</td>
                        <td>{{ item.percentage }}%</td>
                        <td>{{ item.doctor_due|round(2) }} ر.س</td>
                        <td>{{ item.clinic_due|round(2) }} ر.س</td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-primary">
                        <th colspan="3">الإجمالي</th>
                        <th>{{ total_revenue|round(2) }} ر.س</th>
                        <th>-</th>
                        <th>{{ total_doctor_dues|round(2) }} ر.س</th>
                        <th>{{ total_clinic_dues|round(2) }} ر.س</th>
                    </tr>
                </tfoot>
            </table>
        </div>

        <div class="row mt-4">
            <div class="col-md-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">توزيع المستحقات بين الأطباء والعيادة</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="duesDistributionChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> يرجى اختيار الطبيب والشهر والسنة لعرض المستحقات.
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block scripts %}
<!-- Add Select2 CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for doctor dropdown with search functionality
    $('#doctor_id').select2({
        placeholder: 'اختر الطبيب أو اكتب للبحث',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });
});
</script>

{% if doctor_dues %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // بيانات توزيع المستحقات
    var ctx = document.getElementById('duesDistributionChart').getContext('2d');
    var duesChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: [
                {% for item in doctor_dues %}
                '{{ item.doctor.name }}',
                {% endfor %}
                'العيادة'
            ],
            datasets: [{
                data: [
                    {% for item in doctor_dues %}
                    {{ item.doctor_due|round(2) }},
                    {% endfor %}
                    {{ total_clinic_dues|round(2) }}
                ],
                backgroundColor: [
                    '#36a2eb', '#ff6384', '#4bc0c0', '#ffcd56', '#9966ff', '#ff9f40',
                    '#c9cbcf', '#7ccc63', '#fb8072', '#80b1d3'
                ],
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
});
</script>
{% endif %}
{% endblock %}