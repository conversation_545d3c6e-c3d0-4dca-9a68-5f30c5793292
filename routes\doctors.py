from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required
from models import db, Doctor, User

doctors_bp = Blueprint('doctors', __name__)

@doctors_bp.route('/')
@login_required
def index():
    doctors_list = Doctor.query.all()
    return render_template('doctors/index.html', doctors=doctors_list)

@doctors_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if request.method == 'POST':
        # Get form data
        name = request.form.get('name')
        specialization = request.form.get('specialization')
        phone = request.form.get('phone')
        email = request.form.get('email')
        experience_years = request.form.get('experience_years')
        work_schedule = request.form.get('work_schedule')
        percentage = request.form.get('percentage', 50)
        
        # إنشاء حساب مستخدم للطبيب
        username = request.form.get('username')
        password = request.form.get('password')
        
        # التحقق من عدم وجود اسم مستخدم مكرر
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل!', 'danger')
            return redirect(url_for('doctors.add'))
        
        # إنشاء المستخدم
        user = User(username=username, role='طبيب')
        user.set_password(password)
        db.session.add(user)
        db.session.flush()  # للحصول على معرف المستخدم
        
        # إنشاء الطبيب
        new_doctor = Doctor(
            user_id=user.id,
            name=name,
            specialization=specialization,
            phone=phone,
            email=email,
            experience_years=experience_years,
            work_schedule=work_schedule,
            percentage=percentage  # Add percentage here
        )
        
        db.session.add(new_doctor)
        db.session.commit()
        
        flash('تمت إضافة الطبيب بنجاح!', 'success')
        return redirect(url_for('doctors.index'))
    
    return render_template('doctors/add.html')

@doctors_bp.route('/view/<int:doctor_id>')
@login_required
def view(doctor_id):
    """عرض تفاصيل الطبيب"""
    doctor = Doctor.query.get_or_404(doctor_id)
    return render_template('doctors/view.html', doctor=doctor)

@doctors_bp.route('/edit/<int:doctor_id>', methods=['GET', 'POST'])
@login_required
def edit(doctor_id):
    """تعديل بيانات الطبيب"""
    doctor = Doctor.query.get_or_404(doctor_id)
    
    if request.method == 'POST':
        try:
            # Update doctor data
            doctor.name = request.form.get('name')
            doctor.specialization = request.form.get('specialization')
            doctor.phone = request.form.get('phone')
            doctor.email = request.form.get('email')
            doctor.experience_years = request.form.get('experience_years')
            doctor.work_schedule = request.form.get('work_schedule')
            doctor.percentage = request.form.get('percentage', 50)  # Add this line
            
            db.session.commit()
            flash('تم تحديث بيانات الطبيب بنجاح', 'success')
            return redirect(url_for('doctors.view', doctor_id=doctor.id))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث بيانات الطبيب: {str(e)}', 'danger')
    
    return render_template('doctors/edit.html', doctor=doctor)

@doctors_bp.route('/delete_confirm/<int:doctor_id>')
@login_required
def delete_confirm(doctor_id):
    doctor = Doctor.query.get_or_404(doctor_id)
    return render_template('doctors/delete_confirm.html', doctor=doctor)

@doctors_bp.route('/delete/<int:doctor_id>', methods=['POST'])
@login_required
def delete(doctor_id):
    doctor = Doctor.query.get_or_404(doctor_id)
    
    try:
        # Print debug information
        print(f"Attempting to delete doctor: {doctor.name} (ID: {doctor.id})")
        
        # Check for related records
        print(f"Related appointments: {len(doctor.appointments)}")
        print(f"Related treatments: {len(doctor.treatments)}")
        print(f"Related invoices: {len(doctor.invoices)}")
        
        # Delete the doctor
        db.session.delete(doctor)
        db.session.commit()
        
        flash(f'تم حذف الطبيب {doctor.name} بنجاح', 'success')
        return redirect(url_for('doctors.index'))
    except Exception as e:
        db.session.rollback()
        print(f"Error deleting doctor: {str(e)}")
        flash(f'حدث خطأ أثناء حذف الطبيب: {str(e)}', 'danger')
        return redirect(url_for('doctors.view', doctor_id=doctor_id))