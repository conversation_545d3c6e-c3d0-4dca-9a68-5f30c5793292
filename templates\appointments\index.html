{% extends "base.html" %}

{% block page_title %}قائمة المواعيد{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة المواعيد</h5>
        <div class="d-flex">
            <a href="{{ url_for('appointments.add') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus-circle me-1"></i> إضافة موعد جديد
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- Advanced Search Bar -->
        <div class="row mb-4">
            <div class="col-md-6 mx-auto">
                <div class="input-group">
                    <span class="input-group-text bg-light"><i class="fas fa-search text-muted"></i></span>
                    <input type="text" class="form-control" id="appointmentSearch" 
                           placeholder="ابحث عن موعد باسم المريض أو رقم الهاتف أو رقم المريض...">
                    <button class="btn btn-outline-secondary" type="button" id="clearAppointmentSearch">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="searchResults" class="position-absolute bg-white shadow rounded p-0 d-none" 
                     style="z-index: 1000; width: calc(50% - 2rem); max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6;"></div>
                <small class="text-muted mt-1 d-block text-center">اكتب حرفين على الأقل للبحث</small>
            </div>
        </div>

        <!-- Appointments Table -->
        <div class="table-responsive">
            <table class="table table-hover table-bordered" id="appointmentsTable">
                <thead class="table-light">
                    <tr>
                        <th>رقم الموعد</th>
                        <th>المريض</th>
                        <th>الطبيب</th>
                        <th>تاريخ الموعد</th>
                        <th>وقت الموعد</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for appointment in appointments %}
                    <tr data-patient-id="{{ appointment.patient.id }}" 
                        data-patient-name="{{ appointment.patient.name }}" 
                        data-patient-phone="{{ appointment.patient.phone or '' }}">
                        <td>{{ appointment.id }}</td>
                        <td>{{ appointment.patient.name }}</td>
                        <td>{{ appointment.doctor.name }}</td>
                        <td>{{ appointment.appointment_datetime.strftime('%Y-%m-%d') }}</td>
                        <td>{{ appointment.appointment_datetime.strftime('%H:%M') }}</td>
                        <td>
                            <span class="badge {% if appointment.status == 'مؤكد' %}bg-success{% elif appointment.status == 'ملغي' %}bg-danger{% else %}bg-info{% endif %}">
                                {{ appointment.status }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('appointments.details', appointment_id=appointment.id) }}" class="btn btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('appointments.edit', appointment_id=appointment.id) }}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <!-- Find the delete button in your template and replace it with this -->
                                <form action="{{ url_for('appointments.delete_appointment', appointment_id=appointment.id) }}" method="post" style="display:inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الموعد؟');">
                                    <button type="submit" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Advanced search functionality
        const searchInput = document.getElementById('appointmentSearch');
        const searchResults = document.getElementById('searchResults');
        const clearSearchBtn = document.getElementById('clearAppointmentSearch');
        const appointmentsTable = document.getElementById('appointmentsTable');
        const tableRows = appointmentsTable.querySelectorAll('tbody tr');
        
        // Search functionality
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim().toLowerCase();
            
            if (searchTerm.length < 2) {
                // Show all rows when search is cleared or too short
                tableRows.forEach(row => {
                    row.style.display = '';
                });
                searchResults.classList.add('d-none');
                return;
            }
            
            // Advanced search with multiple criteria and scoring
            const matches = Array.from(tableRows).map(row => {
                let score = 0;
                
                const patientId = row.dataset.patientId;
                const patientName = row.dataset.patientName.toLowerCase();
                const patientPhone = row.dataset.patientPhone.toLowerCase();
                
                // Name match (high priority)
                if (patientName.includes(searchTerm)) {
                    score += 10;
                    // Exact match or starts with gets higher score
                    if (patientName === searchTerm) {
                        score += 5;
                    } else if (patientName.startsWith(searchTerm)) {
                        score += 3;
                    }
                }
                
                // Phone match
                if (patientPhone && patientPhone.includes(searchTerm)) {
                    score += 8;
                }
                
                // Patient ID match (direct match with the ID number)
                if (patientId === searchTerm) {
                    score += 15; // Highest priority for exact ID match
                } else if (patientId.includes(searchTerm)) {
                    score += 12; // High priority for partial ID match
                }
                
                return { row, score };
            }).filter(item => item.score > 0)
              .sort((a, b) => b.score - a.score);
            
            // Hide all rows first
            tableRows.forEach(row => {
                row.style.display = 'none';
            });
            
            if (matches.length > 0) {
                // Show matching rows
                matches.forEach(({ row }) => {
                    row.style.display = '';
                });
                
                // Hide search results dropdown
                searchResults.classList.add('d-none');
            } else {
                // Show "no results" message
                searchResults.innerHTML = '<div class="p-3 text-center">لا توجد نتائج</div>';
                searchResults.classList.remove('d-none');
            }
        });
        
        // Clear search button
        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            tableRows.forEach(row => {
                row.style.display = '';
            });
            searchResults.classList.add('d-none');
        });
        
        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                searchResults.classList.add('d-none');
            }
        });
        
        // Delete appointment functionality
        document.querySelectorAll('.delete-appointment').forEach(button => {
            button.addEventListener('click', function() {
                const appointmentId = this.dataset.id;
                if (confirm('هل أنت متأكد من حذف هذا الموعد؟')) {
                    window.location.href = `/appointments/delete/${appointmentId}`;
                }
            });
        });
    });
</script>

<style>
    .search-item {
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .search-item:hover, .search-item.active {
        background-color: #f0f7ff;
        border-right: 3px solid #0d6efd;
    }
    .search-item:last-child {
        border-bottom: none !important;
    }
    #searchResults {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-radius: 0.375rem;
    }
    #searchResults:empty {
        display: none !important;
    }
    #appointmentSearch:focus {
        box-shadow: none;
        border-color: #86b7fe;
    }
</style>
{% endblock %}