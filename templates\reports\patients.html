{% extends "base.html" %}

{% block page_title %}تقرير المرضى{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إحصائيات المرضى</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي المرضى</h5>
                                <h2 class="mb-0">{{ total_patients }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">المرضى الجدد هذا الشهر</h5>
                                <h2 class="mb-0">{{ new_patients_count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title">متوسط عمر المرضى</h5>
                                <h2 class="mb-0">{{ avg_age }} سنة</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <h5 class="card-title">نسبة الذكور/الإناث</h5>
                                <h2 class="mb-0">{{ male_count }}/{{ female_count }}</h2>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">توزيع المرضى حسب الجنس</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="genderChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">المرضى الأكثر زيارة</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>اسم المريض</th>
                                                <th>عدد الزيارات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for patient, count in most_frequent %}
                                            <tr>
                                                <td>{{ patient.name }}</td>
                                                <td>{{ count }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة المرضى</h5>
        <div>
            <a href="{{ url_for('patients.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> إضافة مريض جديد
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            <a href="{{ url_for('reports.patients_report', sort='name', order='asc' if sort_by == 'name' and order == 'desc' else 'desc') }}">
                                اسم المريض
                                {% if sort_by == 'name' %}
                                <i class="fas fa-sort-{{ 'up' if order == 'asc' else 'down' }}"></i>
                                {% endif %}
                            </a>
                        </th>
                        <th>رقم الهاتف</th>
                        <th>
                            <a href="{{ url_for('reports.patients_report', sort='birth_date', order='asc' if sort_by == 'birth_date' and order == 'desc' else 'desc') }}">
                                تاريخ الميلاد
                                {% if sort_by == 'birth_date' %}
                                <i class="fas fa-sort-{{ 'up' if order == 'asc' else 'down' }}"></i>
                                {% endif %}
                            </a>
                        </th>
                        <th>الجنس</th>
                        <th>عدد المواعيد</th>
                        <th>عدد العلاجات</th>
                        <th>عدد الفواتير</th>
                    </tr>
                </thead>
                <tbody>
                    {% for stat in patient_stats %}
                    <tr>
                        <td>{{ stat.patient.name }}</td>
                        <td>{{ stat.patient.phone }}</td>
                        <td>{{ stat.patient.birth_date.strftime('%Y-%m-%d') if stat.patient.birth_date else 'غير محدد' }}</td>
                        <td>{{ stat.patient.gender }}</td>
                        <td>{{ stat.appointments_count }}</td>
                        <td>{{ stat.treatments_count }}</td>
                        <td>{{ stat.invoices_count }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gender distribution chart
        const genderCtx = document.getElementById('genderChart').getContext('2d');
        const genderChart = new Chart(genderCtx, {
            type: 'pie',
            data: {
                labels: ['ذكور', 'إناث'],
                datasets: [{
                    data: [{{ male_count }}, {{ female_count }}],
                    backgroundColor: [
                        '#4e73df',
                        '#e74a3b'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#be2617'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
{% endblock %}