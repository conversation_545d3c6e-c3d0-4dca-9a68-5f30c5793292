{% extends "base.html" %}

{% block page_title %}التقرير المالي{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">التقرير المالي</h5>
        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-right me-1"></i> رجوع
        </a>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">إجمالي الإيرادات</h5>
                        <h2 class="display-4">{{ total_revenue|round(2) }} د.ل</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title">الإيرادات المحصلة</h5>
                        <h2 class="display-4">{{ paid_revenue|round(2) }} د.ل</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <h5 class="card-title">الإيرادات غير المحصلة</h5>
                        <h2 class="display-4">{{ unpaid_revenue|round(2) }} د.ل</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h5 class="card-title">الإيرادات المحصلة جزئياً</h5>
                        <h2 class="display-4">{{ partially_paid_revenue|round(2) }} د.ل</h2>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">الإيرادات الشهرية للسنة الحالية</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyRevenueChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">توزيع الإيرادات حسب الحالة</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">توزيع الإيرادات حسب طريقة الدفع</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="paymentMethodChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // بيانات الإيرادات الشهرية
    var monthlyData = {
        labels: [{% for item in monthly_revenue %}'{{ item.month }}',{% endfor %}],
        datasets: [{
            label: 'الإيرادات (ر.س)',
            data: [{% for item in monthly_revenue %}{{ item.revenue|round(2) }},{% endfor %}],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1,
            tension: 0.1
        }]
    };
    
    // رسم مخطط الإيرادات الشهرية
    var monthlyCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
    var monthlyChart = new Chart(monthlyCtx, {
        type: 'line',
        data: monthlyData,
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // بيانات توزيع الحالة
    var statusData = {
        labels: ['مدفوعة', 'غير مدفوعة', 'مدفوعة جزئياً'],
        datasets: [{
            label: 'الإيرادات (ر.س)',
            data: [{{ paid_revenue|round(2) }}, {{ unpaid_revenue|round(2) }}, {{ partially_paid_revenue|round(2) }}],
            backgroundColor: [
                'rgba(75, 192, 192, 0.6)',
                'rgba(255, 99, 132, 0.6)',
                'rgba(255, 205, 86, 0.6)'
            ],
            borderColor: [
                'rgb(75, 192, 192)',
                'rgb(255, 99, 132)',
                'rgb(255, 205, 86)'
            ],
            borderWidth: 1
        }]
    };
    
    // رسم مخطط توزيع الحالة
    var statusCtx = document.getElementById('statusChart').getContext('2d');
    var statusChart = new Chart(statusCtx, {
        type: 'pie',
        data: statusData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
});
</script>
{% endblock %}