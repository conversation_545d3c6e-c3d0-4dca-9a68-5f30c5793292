{% extends "base.html" %}

{% block page_title %}استيراد بيانات المرضى{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">استيراد بيانات المرضى من ملف Excel</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h6 class="fw-bold">تعليمات الاستيراد:</h6>
            <ul>
                <li>يجب أن يحتوي ملف Excel على الأعمدة التالية: الاسم، تاريخ الميلاد، الجنس، العنوان، رقم الهاتف، البريد الإلكتروني، فصيلة الدم، الحساسية، التاريخ الطبي</li>
                <li>يجب أن يكون عمود "الاسم" و "تاريخ الميلاد" و "الجنس" موجودين وغير فارغين</li>
                <li>يجب أن يكون تنسيق تاريخ الميلاد YYYY-MM-DD</li>
                <li>سيتم تحديث بيانات المرضى الموجودين بالفعل إذا تطابق الاسم وتاريخ الميلاد</li>
            </ul>
        </div>
        
        <form method="POST" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="file" class="form-label">اختر ملف Excel</label>
                <input type="file" class="form-control" id="file" name="file" accept=".xlsx, .xls" required>
            </div>
            
            <div class="mb-3">
                <a href="{{ url_for('patients.export_excel') }}" class="btn btn-outline-primary">
                    <i class="fas fa-download me-1"></i> تنزيل قالب Excel
                </a>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('patients.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> رجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-upload me-1"></i> استيراد البيانات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}